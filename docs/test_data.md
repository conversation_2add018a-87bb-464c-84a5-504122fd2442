# 记账系统测试数据

## 概述

本文档包含记账系统中所有接口的测试数据，用于全面测试系统功能。

## 1. 用户认证相关测试数据

### 1.1 用户登录

**请求数据：**
```json
{
  "type": "login",
  "data": {
    "username": "admin",
    "password": "123456"
  }
}
```

**预期响应：**
```json
{
  "type": "login_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user_info": {
      "id": 1,
      "username": "admin",
      "realname": "管理员",
      "nickname": "Admin",
      "gender": 1,
      "gender_name": "男",
      "avatar": "",
      "mobile": "***********",
      "email": "<EMAIL>",
      "dept_id": 1,
      "status": 1,
      "status_name": "正常",
      "login_time": **********,
      "login_ip": "************",
      "create_time": **********
    },
    "message": "登录成功"
  },
  "timestamp": **********
}
```

### 1.2 用户登出

**请求数据：**
```json
{
  "type": "logout"
}
```

### 1.3 获取用户信息

**请求数据：**
```json
{
  "type": "get_user_info"
}
```

### 1.4 修改密码

**请求数据：**
```json
{
  "type": "change_password",
  "data": {
    "old_password": "123456",
    "new_password": "newpassword123"
  }
}
```

### 1.5 验证令牌

**请求数据：**
```json
{
  "type": "verify_token",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

## 2. 桌台信息相关测试数据

### 2.1 获取桌台信息

**请求数据：**
```json
{
  "type": "get_table_info"
}
```

**预期响应（当客户端IP为************时）：**
```json
{
  "type": "table_info_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "table": {
      "id": 1,
      "table_code": "T001",
      "table_name": "百家乐桌台1",
      "game_type": 1,
      "game_type_name": "百家乐",
      "table_ip": "************",
      "video_url": "rtmp://************/live/table1",
      "channel": 1,
      "channel_name": "现场",
      "wash_user_type": "1,2",
      "wash_rate": 0.0180,
      "max_bet_u": 1000000.00,
      "max_bet_cash": 500000.00,
      "max_bet_chips": 2000000.00,
      "tie_rate": 0.0800,
      "status": 2,
      "status_name": "启用",
      "memo": "主要百家乐桌台，IP为当前电脑",
      "bets": [
        {
          "id": 1,
          "table_id": 1,
          "bet_area": "庄",
          "odds": 1.9500,
          "max_bet_u": 100000.00,
          "max_bet_cash": 50000.00,
          "max_bet_chips": 200000.00,
          "create_time": "2024-01-01 10:00:00"
        },
        {
          "id": 2,
          "table_id": 1,
          "bet_area": "闲",
          "odds": 1.9500,
          "max_bet_u": 100000.00,
          "max_bet_cash": 50000.00,
          "max_bet_chips": 200000.00,
          "create_time": "2024-01-01 10:00:00"
        }
      ],
      "create_time": "2024-01-01 10:00:00"
    },
    "client_ip": "************"
  },
  "timestamp": **********
}
```

## 3. 客户信息相关测试数据

### 3.1 通过洗码号获取客户信息

**请求数据：**
```json
{
  "type": "get_user_by_wash_code",
  "data": {
    "wash_code": "001"
  }
}
```

**预期响应：**
```json
{
  "type": "get_user_by_wash_code_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "user_info": {
      "id": 1,
      "wash_code": "001",
      "account_type": 1,
      "account_type_name": "主号",
      "main_code": "",
      "name": "张三",
      "phone": "***********",
      "user_type": 1,
      "user_type_name": "公司客户",
      "rebate_rate": 0.0180,
      "rebate_rate_text": "1.80%",
      "is_dragon_tiger": 1,
      "dragon_tiger_text": "是",
      "status": 1,
      "status_name": "正常",
      "memo": "VIP客户，主要客户",
      "operator_remark": "优质客户，信用良好",
      "create_time": "2024-01-01 10:00:00",
      "again_create_time": "2024-01-01 10:00:00",
      "update_time": "2024-01-01 15:30:00"
    },
    "wash_code": "001"
  },
  "timestamp": **********
}
```

**分线用户测试数据：**
```json
{
  "type": "get_user_by_wash_code",
  "data": {
    "wash_code": "001-001"
  }
}
```

**预期响应：**
```json
{
  "type": "get_user_by_wash_code_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "user_info": {
      "id": 12,
      "wash_code": "001-001",
      "account_type": 2,
      "account_type_name": "分线",
      "main_code": "001",
      "name": "张三分线1",
      "phone": "***********",
      "user_type": 1,
      "user_type_name": "公司客户",
      "rebate_rate": 0.0180,
      "rebate_rate_text": "1.80%",
      "is_dragon_tiger": 1,
      "dragon_tiger_text": "是",
      "status": 1,
      "status_name": "正常",
      "memo": "001主号分线1",
      "operator_remark": "分线客户",
      "create_time": "2024-01-01 10:00:00",
      "again_create_time": "2024-01-01 10:00:00",
      "update_time": "2024-01-01 15:30:00"
    },
    "wash_code": "001-001"
  },
  "timestamp": **********
}
      "operator_remark": "优质客户",
      "create_time": "2024-01-01 10:00:00",
      "again_create_time": "2024-01-01 10:00:00",
      "update_time": "2024-01-01 15:30:00"
    },
    "wash_code": "WM001"
  },
  "timestamp": **********
}
```

## 4. 洗牌相关测试数据

### 4.1 开始洗牌

**请求数据：**
```json
{
  "type": "start_shuffle",
  "data": {
    "table_id": 1,
    "account_period": "2024-01-15",
    "shoe_no": 1,
    "card_no": 1,
    "shift": 1,
    "shuffle_method": "手动洗牌",
    "card_color": "红色",
    "monitor_id": "M001",
    "monitor_name": "张监场",
    "admin_id": "A001",
    "admin_name": "王管理",
    "shuffle_table_poker": "李洗牌",
    "table_poker": "刘台手",
    "monitor_poker": "陈监牌",
    "cut_card_dealer": "赵切牌"
  }
}
```

**预期响应：**
```json
{
  "type": "start_shuffle_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "shuffle_record": {
      "id": 1,
      "table_id": 1,
      "account_period": "2024-01-15",
      "shoe_no": 1,
      "card_no": 1,
      "shift": 1,
      "shift_name": "早班",
      "shuffle_method": "手动洗牌",
      "card_color": "红色",
      "monitor_id": "M001",
      "monitor_name": "张监场",
      "admin_id": "A001",
      "admin_name": "王管理",
      "shuffle_table_poker": "李洗牌",
      "table_poker": "刘台手",
      "monitor_poker": "陈监牌",
      "cut_card_dealer": "赵切牌",
      "create_time": "2024-01-15 14:30:00"
    },
    "tables_start": {
      "id": 1,
      "table_id": 1,
      "account_period": "2024-01-15",
      "game_type": 1,
      "game_type_name": "百家乐",
      "stats": 1,
      "stats_name": "销售中",
      "shoe_no": 1,
      "hand_no": 0,
      "create_time": "2024-01-15 14:30:00"
    },
    "message": "洗牌开场成功"
  },
  "timestamp": **********
}
```

## 5. 出码相关测试数据

### 5.1 申请出码

**请求数据：**
```json
# 出码申请示例
{
  "type": "apply_out_code",
  "data": {
    "operation_type": 1,
    "account_period": "2024-01-15",
    "amounts": [
      {
        "currency_type": 1,
        "total_amount": 50000.00
      },
      {
        "currency_type": 2,
        "total_amount": 30000.00
      },
      {
        "currency_type": 3,
        "total_amount": 20000.00
      }
    ]
  }
}

# 加彩申请示例
{
  "type": "apply_out_code",
  "data": {
    "operation_type": 3,
    "account_period": "2024-01-15",
    "amounts": [
      {
        "currency_type": 1,
        "total_amount": 10000.00
      }
    ]
  }
}
```

**预期响应：**
```json
{
  "type": "apply_out_code_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "out_code_list": [
      {
        "id": 1,
        "table_id": 1,
        "account_period": "2024-01-15",
        "type": 1,
        "type_name": "出码",
        "status": 2,
        "status_name": "同意",
        "currency_type": 1,
        "currency_name": "筹码",
        "total_amount": 50000.00,
        "operator": "张三",
        "approver": "张三",
        "create_time": "2024-01-15 14:30:00"
      },
      {
        "id": 2,
        "table_id": 1,
        "account_period": "2024-01-15",
        "type": 1,
        "type_name": "出码",
        "status": 2,
        "status_name": "同意",
        "currency_type": 2,
        "currency_name": "现金",
        "total_amount": 30000.00,
        "operator": "张三",
        "approver": "张三",
        "create_time": "2024-01-15 14:30:00"
      },
      {
        "id": 3,
        "table_id": 1,
        "account_period": "2024-01-15",
        "type": 1,
        "type_name": "出码",
        "status": 2,
        "status_name": "同意",
        "currency_type": 3,
        "currency_name": "U码",
        "total_amount": 20000.00,
        "operator": "张三",
        "approver": "张三",
        "create_time": "2024-01-15 14:30:00"
      }
    ],
    "message": "出码申请提交成功"
  },
  "timestamp": **********
}
```

### 5.2 获取出码申请列表

**请求数据：**
```json
{
  "type": "get_out_code_list",
  "data": {
    "table_id": 1,
    "status": 1,
    "limit": 20,
    "offset": 0
  }
}
```

### 5.3 获取出码申请详情

**请求数据：**
```json
{
  "type": "get_out_code_detail",
  "data": {
    "id": 1
  }
}
```

## 6. 下注录入测试数据



## 7. 百家乐结果录入测试数据

### 7.1 百家乐结果录入与结算

**请求数据：**
```json
{
  "type": "enter_result",
  "data": {
    "table_id": 1,
    "account_period": "202406",
    "round_no": 1,
    "hand_no": 1,
    "result": ["庄", "庄对"]
  }
}
```

**预期响应：**
```json
{
  "type": "enter_result_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "结算成功",
    "processed_count": 2,
    "hand_summary": {
      "1": {
        "table_id": 1,
        "account_period": "202406",
        "shoe_no": 1,
        "hand_no": 1,
        "game_type": 1,
        "currency_type": 1,
        "bet_amount": 1700.00,
        "result_1": "庄,庄对",
        "win_loss": 1047.50,
        "loss": 1700.00,
        "amount_tip": 1050.00,
        "amount_bottom": 0.00,
        "wash_rate": 0.00018,
        "wash_amount": 1700.00,
        "wash_tip": 0.31
      },
      "2": {
        "table_id": 1,
        "account_period": "202406",
        "shoe_no": 1,
        "hand_no": 1,
        "game_type": 1,
        "currency_type": 2,
        "bet_amount": 3400.00,
        "result_1": "庄,庄对",
        "win_loss": 2095.00,
        "loss": 3400.00,
        "amount_tip": 2100.00,
        "amount_bottom": 0.00,
        "wash_rate": 0.00018,
        "wash_amount": 3400.00,
        "wash_tip": 0.61
      }
    }
  },
  "timestamp": **********
}
```

## 8. 批量点码测试数据

### 8.1 批量点码

**请求数据：**
```json
{
  "type": "batch_settlement",
  "data": {
    "settlement_records": [
      {
        "table_id": 1,
        "shoe_no": 1,
        "account_period": "202406",
        "currency_type": 1,
        "chips_20w": 2,
        "chips_10w": 5,
        "chips_5w": 10,
        "chips_1w": 20,
        "chips_5k": 50,
        "chips_1k": 100,
        "chips_500": 200,
        "chips_100": 500,
        "chips_50": 1000,
        "chips_10": 5000,
        "chips_5": 10000,
        "total_amount": 1000000.00,
        "client_win_loss": 50000.00,
        "compare_result": "点码与注单比对正常",
        "remark": "第一场次点码",
        "operator": "张三"
      },
      {
        "table_id": 1,
        "shoe_no": 1,
        "account_period": "202406",
        "currency_type": 2,
        "chips_20w": 1,
        "chips_10w": 3,
        "chips_5w": 5,
        "chips_1w": 10,
        "chips_5k": 20,
        "chips_1k": 50,
        "chips_500": 100,
        "chips_100": 200,
        "chips_50": 500,
        "chips_10": 1000,
        "chips_5": 2000,
        "total_amount": 500000.00,
        "client_win_loss": 25000.00,
        "compare_result": "点码与注单比对正常",
        "remark": "第一场次点码",
        "operator": "张三"
      }
    ]
  }
}
```

**预期响应：**
```json
{
  "type": "batch_settlement_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "批量点码成功",
    "settlement_records": [
      {
        "id": 1,
        "table_id": 1,
        "shoe_no": 1,
        "account_period": "2024-06-01",
        "currency_type": 1,
        "chips_20w": 2,
        "chips_10w": 5,
        "chips_5w": 10,
        "chips_1w": 20,
        "chips_5k": 50,
        "chips_1k": 100,
        "chips_500": 200,
        "chips_100": 500,
        "chips_50": 1000,
        "chips_10": 5000,
        "chips_5": 10000,
        "total_amount": 1000000.00,
        "client_win_loss": 50000.00,
        "compare_result": "点码与注单比对正常",
        "remark": "第一场次点码",
        "operator": "张三",
        "create_time": "2024-06-01 15:30:00"
      },
      {
        "id": 2,
        "table_id": 1,
        "shoe_no": 1,
        "account_period": "2024-06-01",
        "currency_type": 2,
        "chips_20w": 1,
        "chips_10w": 3,
        "chips_5w": 5,
        "chips_1w": 10,
        "chips_5k": 20,
        "chips_1k": 50,
        "chips_500": 100,
        "chips_100": 200,
        "chips_50": 500,
        "chips_10": 1000,
        "chips_5": 2000,
        "total_amount": 500000.00,
        "client_win_loss": 25000.00,
        "compare_result": "点码与注单比对正常",
        "remark": "第一场次点码",
        "operator": "张三",
        "create_time": "2024-06-01 15:30:00"
      }
    ]
  },
  "timestamp": **********
}
```

## 9. 收盘测试数据

### 9.1 收盘功能

**请求数据：**
```json
{
  "type": "close_table",
  "data": {
    "table_id": 1
  }
}
```

**预期响应：**
```json
{
  "type": "close_table_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "message": "收盘成功",
    "tables_start": {
      "id": 2,
      "table_id": 1,
      "account_period": "********",
      "game_type": 1,
      "game_type_name": "百家乐",
      "stats": 3,
      "stats_name": "等待出码",
      "shoe_no": 0,
      "hand_no": 0,
      "create_time": "2025-07-11 10:00:00"
    }
  },
  "timestamp": **********
}
```

## 10. 系统用户相关测试数据

### 10.1 通过员工编号查询系统用户

**请求数据：**
```json
{
  "type": "get_sys_user_by_serial",
  "data": {
    "serial_number": 1001
  }
}
```

**预期响应：**
```json
{
  "type": "get_sys_user_by_serial_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "user_info": {
      "id": 1,
      "serial_number": 1001,
      "realname": "张三",
      "nickname": "张经理",
      "gender": 1,
      "gender_name": "男",
      "avatar": "",
      "mobile": "***********",
      "email": "<EMAIL>",
      "dept_id": 1,
      "status": 1,
      "status_name": "正常",
      "login_time": **********,
      "login_ip": "************",
      "create_time": **********
    }
  },
  "timestamp": **********
}
```

## 11. 点码查询测试数据

### 11.1 点码查询

**请求数据：**
```json
{
  "type": "get_code_summary",
  "data": {
    "table_id": 1,
    "account_period": "202406"
  }
}
```

**预期响应：**
```json
{
  "type": "get_code_summary_success",
  "from": "server",
  "to": "客户端ID",
  "data": {
    "table_id": 1,
    "account_period": "202406",
    "items": [
      {
        "currency_type": 1,
        "currency_name": "筹码",
        "out_amount": 50000.00,
        "bonus_amount": 10000.00,
        "total_amount": 60000.00,
        "win_loss": 5000.00,
        "tip_amount": 1000.00,
        "water_amount": 59000.00
      },
      {
        "currency_type": 2,
        "currency_name": "现金",
        "out_amount": 30000.00,
        "bonus_amount": 5000.00,
        "total_amount": 35000.00,
        "win_loss": 3000.00,
        "tip_amount": 500.00,
        "water_amount": 34500.00
      },
      {
        "currency_type": 3,
        "currency_name": "U码",
        "out_amount": 20000.00,
        "bonus_amount": 3000.00,
        "total_amount": 23000.00,
        "win_loss": 2000.00,
        "tip_amount": 300.00,
        "water_amount": 22700.00
      }
    ]
  },
  "timestamp": **********
}
```

## 12. 测试场景组合

### 12.1 完整业务流程测试

1. **登录系统**
   - 使用admin/123456登录
   - 获取JWT令牌

2. **获取桌台信息**
   - 验证IP为************的桌台信息

3. **开始洗牌**
   - 为桌台1开始洗牌
   - 验证洗牌记录和桌台状态更新

4. **申请出码**
   - 为桌台1申请多种货币类型的出码
   - 验证出码记录创建

5. **录入下注**
   - 批量录入多条下注记录
   - 验证Redis存储

6. **录入结果**
   - 录入百家乐游戏结果
   - 验证结算和统计

7. **批量点码**
   - 批量录入点码记录
   - 验证点码与注单比对

8. **收盘**
   - 执行收盘操作
   - 验证新账期创建

### 12.2 错误场景测试

1. **认证错误**
   - 使用错误的用户名密码
   - 使用无效的JWT令牌
   - 未登录访问需要认证的接口

2. **数据验证错误**
   - 提交空数据
   - 提交格式错误的数据
   - 提交超出限制的数据

3. **业务逻辑错误**
   - 重复申请出码
   - 重复录入下注
   - 重复结算
   - 操作禁用状态的桌台

4. **网络错误**
   - WebSocket连接断开
   - 消息发送失败
   - 服务器无响应

## 13. 测试工具使用说明

### 13.1 使用WebSocket测试工具

1. 打开浏览器访问 `http://localhost:8080/static/index.html`
2. 建立WebSocket连接
3. 按照测试数据逐条发送消息
4. 验证响应结果

### 13.2 专用测试页面

#### 下注相关接口测试
- 地址：`http://localhost:8080/static/test_bet_api.html`
- 功能：批量下注录入、百家乐结果录入
- 特点：可视化表单，支持多记录录入

#### 桌台信息测试
- 地址：`http://localhost:8080/static/test_table_api.html`
- 功能：桌台信息查询、洗牌功能

#### 出码申请测试
- 地址：`http://localhost:8080/static/test_out_code_api.html`
- 功能：出码申请、查询、详情

#### 洗牌功能测试
- 地址：`http://localhost:8080/static/test_shuffle_api.html`
- 功能：洗牌记录、桌台状态管理

#### 系统用户测试
- 地址：`http://localhost:8080/static/test_sys_user_api.html`
- 功能：系统用户查询

#### 收盘功能测试
- 地址：`http://localhost:8080/static/test_close_table_api.html`
- 功能：收盘操作、新账期创建

### 13.2 使用Postman测试

1. 创建WebSocket连接
2. 发送JSON格式的消息
3. 验证响应格式和内容

### 13.3 自动化测试

1. 使用JavaScript编写自动化测试脚本
2. 批量执行测试用例
3. 生成测试报告

## 14. 数据库测试数据

### 14.1 用户数据

#### 14.1.1 系统用户数据

```sql
-- 插入系统用户
INSERT INTO sys_user (serial_number, realname, nickname, gender, mobile, email, username, password, status, create_time) VALUES
(1001, '张三', '张经理', 1, '***********', '<EMAIL>', 'admin', '123456', 1, UNIX_TIMESTAMP()),
(1002, '李四', '李主管', 1, '***********', '<EMAIL>', 'lisi', '123456', 1, UNIX_TIMESTAMP()),
(1003, '王五', '王总监', 1, '13800138002', '<EMAIL>', 'wangwu', '123456', 1, UNIX_TIMESTAMP());
```

#### 14.1.2 客户数据

**洗码号格式说明：**
- 主号：3位数字，如 `001`, `002`, `003`
- 分线：主号-分线格式，如 `001-001`, `001-002`, `002-001`

**用户类型说明：**
- 1: 公司客户
- 2: 返点F
- 3: 返点W
- 4: 占成客户
- 5: 特殊客户

**账号状态说明：**
- 1: 正常
- 2: 冻结
- 3: 禁用

**龙虎洗码说明：**
- 1: 是
- 0: 否

```sql
-- 参考 insert_users.sql 文件中的完整用户数据
-- 包含17个主号用户和25个分线用户
-- 涵盖所有用户类型和状态
-- 包含正常、冻结、禁用状态的用户用于测试
```

### 14.2 桌台数据

参考 `insert_tables.sql` 文件中的完整桌台数据。

## 15. 性能测试数据

### 15.1 并发测试

- 同时连接100个WebSocket客户端
- 每秒发送1000条消息
- 测试系统响应时间和稳定性

### 15.2 数据量测试

- 插入10000条下注记录
- 插入1000条点码记录
- 测试查询性能

### 15.3 压力测试

- 模拟高并发场景
- 测试系统极限性能
- 验证错误处理机制

## 16. 安全测试数据

### 16.1 认证安全

- 测试JWT令牌过期处理
- 测试权限验证
- 测试会话管理

### 16.2 数据安全

- 测试SQL注入防护
- 测试XSS攻击防护
- 测试CSRF攻击防护

### 16.3 网络安全

- 测试WebSocket连接安全
- 测试数据传输加密
- 测试防火墙配置

## 总结

本文档提供了完整的测试数据，涵盖了记账系统的所有功能模块。测试人员可以根据这些数据进行全面的功能测试、性能测试和安全测试，确保系统的稳定性和可靠性。 