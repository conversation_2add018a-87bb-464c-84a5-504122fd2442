package services

import (
	"accounting_enter/internal/database"
	"accounting_enter/internal/models"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// BetService 下注服务
type BetService struct{}

// NewBetService 创建下注服务实例
func NewBetService() *BetService {
	return &BetService{}
}

// BetRecordRequest 下注记录请求结构
type BetRecordRequest struct {
	TableID          int64   `json:"table_id" binding:"required"`       // 桌台ID (在批量下注时通过IP获取)
	GameType         int8    `json:"game_type" binding:"required"`      // 游戏类型:1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;-轮盘;
	AccountPeriod    string  `json:"account_period" binding:"required"` // 账期
	RoundNo          int     `json:"round_no" binding:"required"`       // 场次编号
	HandNo           int     `json:"hand_no" binding:"required"`        // 局号编号
	WashCode         string  `json:"wash_code" binding:"required"`      // 洗码号
	UserName         string  `json:"user_name"`                         // 客户姓名
	CurrencyType     int8    `json:"currency_type" binding:"required"`  // 货币类型:1-筹码;2-现金;3-U码
	BankerAmount     float64 `json:"banker_amount"`                     // 庄-龙
	PlayerAmount     float64 `json:"player_amount"`                     // 闲-虎
	TieAmount        float64 `json:"tie_amount"`                        // 和
	BankerPairAmount float64 `json:"banker_pair_amount"`                // 庄对
	PlayerPairAmount float64 `json:"player_pair_amount"`                // 闲对
	Lucky6Amount     float64 `json:"lucky_6_amount"`                    // 幸运6
	Lucky7Amount     float64 `json:"lucky_7_amount"`                    // 幸运7
	WinResult        string  `json:"win_result"`                        // 结果
	WinLoss          float64 `json:"win_loss"`                          // 输赢金额
	Loss             float64 `json:"loss"`                              // 输口
	AmountTip        float64 `json:"amount_tip"`                        // 小费金额
	AmountBottom     float64 `json:"amount_bottom"`                     // 和底（通用底注）
	WashRate         float64 `json:"wash_rate"`                         // 洗码率
	WashAmount       float64 `json:"wash_amount"`                       // 本局洗码量
	WashTip          float64 `json:"wash_tip"`                          // 洗码费
}

// BetRecordResponse 下注记录响应结构
type BetRecordResponse struct {
	ID               int64   `json:"id"`
	TableID          int64   `json:"table_id"`
	AccountPeriod    string  `json:"account_period"`
	RoundNo          int     `json:"round_no"`
	HandNo           int     `json:"hand_no"`
	WashCode         string  `json:"wash_code"`
	UserName         string  `json:"user_name"`
	CurrencyType     int8    `json:"currency_type"`
	CurrencyTypeName string  `json:"currency_type_name"`
	BankerAmount     float64 `json:"banker_amount"`
	PlayerAmount     float64 `json:"player_amount"`
	TieAmount        float64 `json:"tie_amount"`
	BankerPairAmount float64 `json:"banker_pair_amount"`
	PlayerPairAmount float64 `json:"player_pair_amount"`
	Lucky6Amount     float64 `json:"lucky_6_amount"`
	Lucky7Amount     float64 `json:"lucky_7_amount"`
	TotalBetAmount   float64 `json:"total_bet_amount"`
	WinResult        string  `json:"win_result"`
	WinLoss          float64 `json:"win_loss"`
	Loss             float64 `json:"loss"`
	AmountTip        float64 `json:"amount_tip"`
	AmountBottom     float64 `json:"amount_bottom"`
	WashRate         float64 `json:"wash_rate"`
	WashAmount       float64 `json:"wash_amount"`
	WashTip          float64 `json:"wash_tip"`
	CreateTime       string  `json:"create_time"`
}

// EnterResultRequest 结果录入请求结构体
// 用于百家乐结果录入
// 入参：账期、场次编号、局号编号、结果（可多项组合）
type EnterResultRequest struct {
	AccountPeriod string   `json:"account_period" binding:"required"` // 账期
	RoundNo       int      `json:"round_no" binding:"required"`       // 场次编号
	HandNo        int      `json:"hand_no" binding:"required"`        // 局号编号
	Result        []string `json:"result" binding:"required"`         // 结果项（如["庄","庄对","闲对"]）
}

// BatchBetRequest 批量下注请求结构
type BatchBetRequest struct {
	BetRecords []BetRecordRequest `json:"bet_records" binding:"required"` // 下注记录数组
}

// BatchBetResponse 批量下注响应结构
type BatchBetResponse struct {
	Success        bool              `json:"success"`         // 整体是否成功
	Message        string            `json:"message"`         // 操作结果消息
	TableID        int64             `json:"table_id"`        // 桌台ID
	ProcessedCount int               `json:"processed_count"` // 处理的记录数量
	Results        []SingleBetResult `json:"results"`         // 每条记录的处理结果
}

// SingleBetResult 单条下注记录处理结果
type SingleBetResult struct {
	Success      bool   `json:"success"`       // 是否成功
	Message      string `json:"message"`       // 处理消息
	WashCode     string `json:"wash_code"`     // 洗码号
	CurrencyType int8   `json:"currency_type"` // 货币类型
	RedisKey     string `json:"redis_key"`     // Redis键名
	Index        int    `json:"index"`         // 在批量请求中的索引
}

// EnterResult 录入结果并结算
func (s *BetService) EnterResult(req EnterResultRequest, clientIP string) (map[string]interface{}, error) {
	// 参数验证
	if clientIP == "" {
		return nil, errors.New("客户端IP不能为空")
	}

	// 通过IP获取桌台ID
	tableService := NewTableService()
	tableID, err := tableService.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}
	if req.AccountPeriod == "" {
		return nil, errors.New("账期不能为空")
	}
	if req.RoundNo <= 0 {
		return nil, errors.New("场次编号不能为空")
	}
	if req.HandNo <= 0 {
		return nil, errors.New("局号编号不能为空")
	}
	if len(req.Result) == 0 {
		return nil, errors.New("结果不能为空")
	}

	// 验证账期格式
	if _, err := time.Parse("********", req.AccountPeriod); err != nil {
		return nil, errors.New("账期格式错误，请使用 YYYYMMDD 格式")
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err = database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	// 从Redis获取该局的所有下注记录
	ctx := context.Background()

	// 使用新的用户下注记录查询模式
	userBetPattern := s.generateUserBetPattern(tableID, req.AccountPeriod, req.RoundNo, req.HandNo)
	keys, err := database.RedisClient.Keys(ctx, userBetPattern).Result()
	if err != nil {
		return nil, fmt.Errorf("获取Redis下注记录失败: %v", err)
	}

	if len(keys) == 0 {
		return nil, errors.New("该局没有找到下注记录")
	}

	log.Printf("查找用户下注记录，匹配模式: %s, 找到 %d 个洗码号的记录", userBetPattern, len(keys))

	var betRecords []models.BetRecord
	var totalByCurrency = make(map[string]map[string]float64)

	// 开启数据库事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取所有下注记录
	for _, key := range keys {
		// 从Redis获取下注记录（每个key存储一个洗码号的多条记录数组）
		betRecordJSON, err := database.RedisClient.Get(ctx, key).Result()
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("获取Redis记录失败: %v", err)
		}

		// 反序列化为记录数组（兼容新旧格式）
		var userBetRecords []BetRecordRequest

		// 先尝试解析为数组格式
		err = json.Unmarshal([]byte(betRecordJSON), &userBetRecords)
		if err != nil {
			// 如果数组格式失败，尝试解析为单条记录格式
			var singleRecord BetRecordRequest
			err = json.Unmarshal([]byte(betRecordJSON), &singleRecord)
			if err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("反序列化下注记录失败: %v", err)
			}
			userBetRecords = []BetRecordRequest{singleRecord}
		}

		// 处理该洗码号的所有记录
		for _, betReq := range userBetRecords {

			// 检查数据库中是否已存在该记录（避免重复结算）
			var existingRecord models.BetRecord
			err = tx.Where(
				"wash_code = ? AND table_id = ? AND account_period = ? AND hand_no = ? AND currency_type = ?",
				betReq.WashCode, betReq.TableID, betReq.AccountPeriod, betReq.HandNo, betReq.CurrencyType,
			).First(&existingRecord).Error

			if err == nil {
				// 记录已存在，跳过
				continue
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				tx.Rollback()
				return nil, fmt.Errorf("检查记录唯一性失败: %v", err)
			}

			// 计算结算结果
			winLoss, loss, washAmount, washTip, amountTip := s.calculateBaccaratResult(betReq, req.Result, table.WashRate, tableID)

			// 创建下注记录
			betRecord := models.BetRecord{
				TableID:          betReq.TableID,
				TablesName:       table.TablesName,
				GameType:         betReq.GameType,
				AccountPeriod:    betReq.AccountPeriod,
				RoundNo:          betReq.RoundNo,
				HandNo:           betReq.HandNo,
				WashCode:         betReq.WashCode,
				UserName:         betReq.UserName,
				CurrencyType:     betReq.CurrencyType,
				BankerAmount:     betReq.BankerAmount,
				PlayerAmount:     betReq.PlayerAmount,
				TieAmount:        betReq.TieAmount,
				BankerPairAmount: betReq.BankerPairAmount,
				PlayerPairAmount: betReq.PlayerPairAmount,
				Lucky6Amount:     betReq.Lucky6Amount,
				Lucky7Amount:     betReq.Lucky7Amount,
				WinResult:        strings.Join(req.Result, ","),
				WinLoss:          winLoss,
				Loss:             loss,
				AmountTip:        amountTip,
				AmountBottom:     betReq.AmountBottom,
				WashRate:         table.WashRate,
				WashAmount:       washAmount,
				WashTip:          washTip,
				CreateTime:       time.Now(),
			}

			// 保存到数据库
			err = tx.Create(&betRecord).Error
			if err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("保存下注记录失败: %v", err)
			}

			betRecords = append(betRecords, betRecord)

			// 统计各货币类型的总输赢
			currencyName := s.getCurrencyTypeName(betRecord.CurrencyType)
			if totalByCurrency[currencyName] == nil {
				totalByCurrency[currencyName] = make(map[string]float64)
			}
			totalByCurrency[currencyName]["win_loss"] += betRecord.WinLoss
			totalByCurrency[currencyName]["loss"] += betRecord.Loss

		}

		// 结算后删除Redis记录
		err = database.RedisClient.Del(ctx, key).Err()
		if err != nil {
			// 删除失败不影响主流程，只记录错误
			fmt.Printf("删除Redis记录失败: %v\n", err)
		}
	}

	// 根据货币类型统计并创建hand_records记录
	currencyStats := make(map[int8]map[string]float64)
	for _, betRecord := range betRecords {
		currencyType := betRecord.CurrencyType
		if currencyStats[currencyType] == nil {
			currencyStats[currencyType] = make(map[string]float64)
		}

		// 计算下注金额总和
		totalBetAmount := betRecord.BankerAmount + betRecord.PlayerAmount + betRecord.TieAmount +
			betRecord.BankerPairAmount + betRecord.PlayerPairAmount + betRecord.Lucky6Amount + betRecord.Lucky7Amount

		currencyStats[currencyType]["bet_amount"] += totalBetAmount
		currencyStats[currencyType]["win_loss"] += betRecord.WinLoss
		currencyStats[currencyType]["loss"] += betRecord.Loss
		currencyStats[currencyType]["amount_tip"] += betRecord.AmountTip
		currencyStats[currencyType]["amount_bottom"] += betRecord.AmountBottom
		currencyStats[currencyType]["wash_amount"] += betRecord.WashAmount
		currencyStats[currencyType]["wash_tip"] += betRecord.WashTip
	}

	// 为每种货币类型创建hand_records记录
	for currencyType, stats := range currencyStats {
		handRecord := models.HandRecord{
			TableID:       tableID,
			TablesName:    table.TablesName,
			AccountPeriod: req.AccountPeriod,
			ShoeNo:        req.RoundNo,
			HandNo:        req.HandNo,
			GameType:      1, // 百家乐游戏类型
			CurrencyType:  currencyType,
			BetAmount:     stats["bet_amount"],
			Result1:       strings.Join(req.Result, ","),
			WinLoss:       stats["win_loss"],
			Loss:          stats["loss"],
			AmountTip:     stats["amount_tip"],
			AmountBottom:  stats["amount_bottom"],
			WashRate:      table.WashRate,
			WashAmount:    stats["wash_amount"],
			WashTip:       stats["wash_tip"],
			CreateTime:    time.Now(),
		}

		err = tx.Create(&handRecord).Error
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("保存手牌记录失败: %v", err)
		}
	}

	// tables_start 局号+1
	tx.Model(&models.TablesStart{}).Where("table_id = ? AND account_period = ? AND shoe_no = ?",
		tableID, req.AccountPeriod, req.RoundNo).UpdateColumn("hand_no", gorm.Expr("hand_no + 1"))

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	return map[string]interface{}{
		"message":         "结算成功",
		"processed_count": len(betRecords),
		"hand_summary":    totalByCurrency,
	}, nil

}

// validateBetRecord 验证单条下注记录（增强版）
func (s *BetService) validateBetRecord(req BetRecordRequest) error {
	// 基本字段验证
	if req.TableID <= 0 {
		return errors.New("桌台ID不能为空或无效")
	}

	// 游戏类型验证
	if req.GameType < 1 || req.GameType > 9 {
		return errors.New("游戏类型无效，应为1-9之间的数字")
	}

	// 货币类型验证
	if req.CurrencyType < 1 || req.CurrencyType > 3 {
		return errors.New("货币类型无效，应为1-筹码、2-现金、3-U码")
	}

	// 使用增强的账期验证
	if err := s.validateAccountPeriod(req.AccountPeriod); err != nil {
		return fmt.Errorf("账期验证失败: %v", err)
	}

	// 使用增强的场次和局号验证
	if err := s.validateGameRounds(req.RoundNo, req.HandNo); err != nil {
		return fmt.Errorf("场次/局号验证失败: %v", err)
	}

	// 使用增强的洗码号验证
	if err := s.validateWashCode(req.WashCode); err != nil {
		return fmt.Errorf("洗码号验证失败: %v", err)
	}

	// 使用增强的用户名验证
	if err := s.validateUserName(req.UserName); err != nil {
		return fmt.Errorf("用户名验证失败: %v", err)
	}

	// 使用增强的下注金额验证
	if err := s.validateBetAmounts(req); err != nil {
		return fmt.Errorf("下注金额验证失败: %v", err)
	}

	// 验证桌台是否存在且启用（增强错误处理）
	var table models.Table
	err := database.DB.Where("id = ? AND status = ?", req.TableID, 2).First(&table).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("桌台ID %d 不存在或未启用", req.TableID)
		}
		return fmt.Errorf("查询桌台信息失败: %v", err)
	}

	// 验证游戏类型与桌台配置是否匹配
	if table.GameType != req.GameType {
		return fmt.Errorf("游戏类型不匹配，桌台游戏类型为%d，请求游戏类型为%d", table.GameType, req.GameType)
	}

	return nil
}

// FormatBetRecordResponse 格式化下注记录响应数据
func (s *BetService) FormatBetRecordResponse(record *models.BetRecord) *BetRecordResponse {
	totalBetAmount := record.BankerAmount + record.PlayerAmount + record.TieAmount +
		record.BankerPairAmount + record.PlayerPairAmount + record.Lucky6Amount + record.Lucky7Amount

	return &BetRecordResponse{
		ID:               record.ID,
		TableID:          record.TableID,
		AccountPeriod:    record.AccountPeriod,
		RoundNo:          record.RoundNo,
		HandNo:           record.HandNo,
		WashCode:         record.WashCode,
		UserName:         record.UserName,
		CurrencyType:     record.CurrencyType,
		CurrencyTypeName: s.getCurrencyTypeName(record.CurrencyType),
		BankerAmount:     record.BankerAmount,
		PlayerAmount:     record.PlayerAmount,
		TieAmount:        record.TieAmount,
		BankerPairAmount: record.BankerPairAmount,
		PlayerPairAmount: record.PlayerPairAmount,
		Lucky6Amount:     record.Lucky6Amount,
		Lucky7Amount:     record.Lucky7Amount,
		TotalBetAmount:   totalBetAmount,
		WinResult:        record.WinResult,
		WinLoss:          record.WinLoss,
		Loss:             record.Loss,
		AmountTip:        record.AmountTip,
		AmountBottom:     record.AmountBottom,
		WashRate:         record.WashRate,
		WashAmount:       record.WashAmount,
		WashTip:          record.WashTip,
		CreateTime:       record.CreateTime.Format("2006-01-02 15:04:05"),
	}
}

// getCurrencyTypeName 获取货币类型名称
func (s *BetService) getCurrencyTypeName(currencyType int8) string {
	switch currencyType {
	case 1:
		return "筹码"
	case 2:
		return "现金"
	case 3:
		return "U码"
	default:
		return "未知"
	}
}

// calculateBaccaratResult 计算百家乐结果
func (s *BetService) calculateBaccaratResult(betReq BetRecordRequest, result []string, washRate float64, tableID int64) (float64, float64, float64, float64, float64) {
	var winLoss float64 = 0
	var loss float64 = 0
	var washAmount float64 = 0
	var washTip float64 = 0
	var amountTip float64 = 0

	// 从tables_bets表获取该桌台的赔率配置
	var tableBets []models.TableBet
	err := database.DB.Where("table_id = ?", tableID).Find(&tableBets).Error
	if err != nil {
		log.Printf("获取桌台赔率配置失败: %v", err)
		// 如果获取失败，使用默认赔率
		tableBets = s.getDefaultOdds()
	}

	// 构建赔率映射
	oddsMap := make(map[string]float64)
	for _, bet := range tableBets {
		oddsMap[bet.BetArea] = bet.Odds
	}

	// 从tables表获取洗码率
	var table models.Table
	err = database.DB.Where("id = ?", tableID).First(&table).Error
	if err != nil {
		log.Printf("获取桌台洗码率失败: %v", err)
		// 使用传入的默认洗码率
	} else {
		// 使用桌台配置的洗码率
		washRate = table.WashRate
	}

	// 计算总投注金额
	totalBetAmount := betReq.BankerAmount + betReq.PlayerAmount + betReq.TieAmount +
		betReq.BankerPairAmount + betReq.PlayerPairAmount + betReq.Lucky6Amount + betReq.Lucky7Amount

	// 标记主要结果和副作用结果
	var mainResult string    // 主要结果：庄、闲、和
	var sideResults []string // 副作用结果：庄对、闲对、幸运6、幸运7

	// 分类结果
	for _, res := range result {
		switch res {
		case "庄", "闲", "和":
			mainResult = res
		case "庄对", "闲对", "幸运6", "幸运7":
			sideResults = append(sideResults, res)
		}
	}

	// 创建副作用结果映射，用于快速查找
	sideResultsMap := make(map[string]bool)
	for _, sideRes := range sideResults {
		sideResultsMap[sideRes] = true
	}

	// 1. 计算主要结果的输赢
	switch mainResult {
	case "庄":
		// 庄赢：使用配置的赔率，赔率已经是净赔率（扣除佣金后）
		bankerOdds := oddsMap["庄"]
		if bankerOdds == 0 {
			bankerOdds = 0.95 // 默认赔率（扣除5%佣金）
		}
		// 庄投注：返还本金 + 赔付
		winLoss += betReq.BankerAmount + (betReq.BankerAmount * bankerOdds)
		// 闲投注和和投注输掉（不返还本金）
		// winLoss -= betReq.PlayerAmount // 已经在总投注中扣除
		// winLoss -= betReq.TieAmount    // 已经在总投注中扣除
	case "闲":
		// 闲赢：1:1赔付
		playerOdds := oddsMap["闲"]
		if playerOdds == 0 {
			playerOdds = 1.0 // 默认1:1赔率
		}
		// 闲投注：返还本金 + 赔付
		winLoss += betReq.PlayerAmount + (betReq.PlayerAmount * playerOdds)
		// 庄投注和和投注输掉（不返还本金）
	case "和":
		// 和：使用配置的赔率
		tieOdds := oddsMap["和"]
		if tieOdds == 0 {
			tieOdds = 8.0 // 默认赔率
		}
		// 和投注：返还本金 + 赔付
		winLoss += betReq.TieAmount + (betReq.TieAmount * tieOdds)
		// 庄投注和闲投注输掉（不返还本金）
		// 和的投注本金变为小费（百家乐规则）
		amountTip += betReq.TieAmount
	}

	// 2. 计算副作用投注的输赢
	// 庄对
	if sideResultsMap["庄对"] {
		// 庄对中奖
		bankerPairOdds := oddsMap["庄对"]
		if bankerPairOdds == 0 {
			bankerPairOdds = 11.0 // 默认赔率
		}
		winLoss += betReq.BankerPairAmount + (betReq.BankerPairAmount * bankerPairOdds)
	}
	// 如果庄对没中奖，投注金额已经在总投注中扣除

	// 闲对
	if sideResultsMap["闲对"] {
		// 闲对中奖
		playerPairOdds := oddsMap["闲对"]
		if playerPairOdds == 0 {
			playerPairOdds = 11.0 // 默认赔率
		}
		winLoss += betReq.PlayerPairAmount + (betReq.PlayerPairAmount * playerPairOdds)
	}

	// 幸运6
	if sideResultsMap["幸运6"] {
		// 幸运6中奖
		lucky6Odds := oddsMap["幸运6"]
		if lucky6Odds == 0 {
			lucky6Odds = 12.0 // 默认赔率
		}
		winLoss += betReq.Lucky6Amount + (betReq.Lucky6Amount * lucky6Odds)
	}

	// 幸运7
	if sideResultsMap["幸运7"] {
		// 幸运7中奖
		lucky7Odds := oddsMap["幸运7"]
		if lucky7Odds == 0 {
			lucky7Odds = 50.0 // 默认赔率
		}
		winLoss += betReq.Lucky7Amount + (betReq.Lucky7Amount * lucky7Odds)
	}

	// 3. 计算最终输赢（客户视角）
	// winLoss = 客户获得的总金额 - 客户投注的总金额
	winLoss = winLoss - totalBetAmount

	// 4. 计算输口（客户损失的金额）
	if winLoss < 0 {
		// 客户输了
		loss = -winLoss
	} else {
		// 客户赢了，输口为0
		loss = 0
	}

	// 5. 计算洗码量（业务规则：洗码量都是单边戏码，输口=洗码量）
	washAmount = loss

	// 6. 统一处理小费计算（避免重复计算）
	// 检查是否有和底，以及哪些结果会导致和底变为小费
	if betReq.AmountBottom > 0 {
		// 和、庄对、闲对任一结果出现时，和底变为小费
		if sideResultsMap["庄对"] || sideResultsMap["闲对"] {
			amountTip += betReq.AmountBottom
		}
	}

	// 7. 计算洗码费
	washTip = washAmount * washRate

	log.Printf("计算结果 - 洗码号: %s, 总投注: %.2f, 输赢: %.2f, 输口: %.2f, 洗码量: %.2f, 洗码费: %.2f, 小费: %.2f",
		betReq.WashCode, totalBetAmount, winLoss, loss, washAmount, washTip, amountTip)

	return winLoss, loss, washAmount, washTip, amountTip
}

// getDefaultOdds 获取默认赔率配置（当数据库查询失败时使用）
// 注意：这里的赔率是净赔率，不包含本金
func (s *BetService) getDefaultOdds() []models.TableBet {
	return []models.TableBet{
		{BetArea: "庄", Odds: 0.9500},    // 庄赢1:0.95（扣除5%佣金）
		{BetArea: "闲", Odds: 1.0000},    // 闲赢1:1
		{BetArea: "和", Odds: 8.0000},    // 和赢1:8
		{BetArea: "庄对", Odds: 11.0000},  // 庄对1:11
		{BetArea: "闲对", Odds: 11.0000},  // 闲对1:11
		{BetArea: "幸运6", Odds: 12.0000}, // 幸运6 1:12
		{BetArea: "幸运7", Odds: 50.0000}, // 幸运7 1:50
	}
}

// QueryBetRecordsRequest 查询下注记录请求结构
type QueryBetRecordsRequest struct {
	AccountPeriod string `json:"account_period" binding:"required"` // 账期
	RoundNo       int    `json:"round_no" binding:"required"`       // 场次编号
	HandNo        int    `json:"hand_no" binding:"required"`        // 局号编号
}

// QueryBetRecordsResponse 查询下注记录响应结构
type QueryBetRecordsResponse struct {
	Success        bool                `json:"success"`
	Message        string              `json:"message"`
	TableID        int64               `json:"table_id"`
	TableName      string              `json:"table_name"`
	AccountPeriod  string              `json:"account_period"`
	RoundNo        int                 `json:"round_no"`
	HandNo         int                 `json:"hand_no"`
	RecordCount    int                 `json:"record_count"`
	BetRecords     []BetRecordResponse `json:"bet_records"`
	TotalsByStatus map[string]float64  `json:"totals_by_status"`
}

// QueryBetRecordsFromRedis 从Redis查询该局的所有下注记录
func (s *BetService) QueryBetRecordsFromRedis(req QueryBetRecordsRequest, clientIP string) (*QueryBetRecordsResponse, error) {
	// 参数验证
	if clientIP == "" {
		return nil, errors.New("客户端IP不能为空")
	}

	// 通过IP获取桌台信息
	tableService := NewTableService()
	tableID, err := tableService.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err = database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	// 验证账期格式
	if _, err := time.Parse("********", req.AccountPeriod); err != nil {
		return nil, errors.New("账期格式错误，请使用 YYYYMMDD 格式")
	}

	if req.RoundNo <= 0 {
		return nil, errors.New("场次编号必须大于0")
	}

	if req.HandNo <= 0 {
		return nil, errors.New("局号编号必须大于0")
	}

	ctx := context.Background()

	// 使用新的Redis键名模式生成方法
	redisPattern := s.generateBetRecordPattern(tableID, req.AccountPeriod, req.RoundNo, req.HandNo)

	log.Printf("开始查询Redis下注记录，桌台ID: %d, 桌台名称: %s, 账期: %s, 场次: %d, 局号: %d, 匹配模式: %s",
		tableID, table.TablesName, req.AccountPeriod, req.RoundNo, req.HandNo, redisPattern)

	// 从Redis获取匹配的键
	keys, err := database.RedisClient.Keys(ctx, redisPattern).Result()
	if err != nil {
		return nil, fmt.Errorf("查询Redis键失败: %v", err)
	}

	if len(keys) == 0 {
		log.Printf("未找到任何下注记录，桌台ID: %d, 账期: %s, 场次: %d, 局号: %d",
			tableID, req.AccountPeriod, req.RoundNo, req.HandNo)

		return &QueryBetRecordsResponse{
			Success:        true,
			Message:        "该局暂无下注记录",
			TableID:        tableID,
			TableName:      table.TablesName,
			AccountPeriod:  req.AccountPeriod,
			RoundNo:        req.RoundNo,
			HandNo:         req.HandNo,
			RecordCount:    0,
			BetRecords:     []BetRecordResponse{},
			TotalsByStatus: make(map[string]float64),
		}, nil
	}

	var betRecords []BetRecordResponse
	var totalsByStatus = make(map[string]float64)

	// 按货币类型统计总下注金额
	currencyTotals := make(map[string]float64)

	// 遍历所有匹配的键，获取下注记录
	for _, key := range keys {
		// 从Redis获取记录内容
		betRecordJSON, err := database.RedisClient.Get(ctx, key).Result()
		if err != nil {
			log.Printf("获取Redis记录失败，键: %s, 错误: %v", key, err)
			continue
		}

		// 反序列化下注记录
		var betReq BetRecordRequest
		err = json.Unmarshal([]byte(betRecordJSON), &betReq)
		if err != nil {
			log.Printf("反序列化下注记录失败，键: %s, 错误: %v", key, err)
			continue
		}

		// 计算总下注金额
		totalBetAmount := betReq.BankerAmount + betReq.PlayerAmount + betReq.TieAmount +
			betReq.BankerPairAmount + betReq.PlayerPairAmount + betReq.Lucky6Amount + betReq.Lucky7Amount

		// 格式化响应数据
		betRecord := BetRecordResponse{
			TableID:          betReq.TableID,
			AccountPeriod:    betReq.AccountPeriod,
			RoundNo:          betReq.RoundNo,
			HandNo:           betReq.HandNo,
			WashCode:         betReq.WashCode,
			UserName:         betReq.UserName,
			CurrencyType:     betReq.CurrencyType,
			CurrencyTypeName: s.getCurrencyTypeName(betReq.CurrencyType),
			BankerAmount:     betReq.BankerAmount,
			PlayerAmount:     betReq.PlayerAmount,
			TieAmount:        betReq.TieAmount,
			BankerPairAmount: betReq.BankerPairAmount,
			PlayerPairAmount: betReq.PlayerPairAmount,
			Lucky6Amount:     betReq.Lucky6Amount,
			Lucky7Amount:     betReq.Lucky7Amount,
			TotalBetAmount:   totalBetAmount,
			WinResult:        betReq.WinResult, // 使用原始记录中的结果状态
			WinLoss:          betReq.WinLoss,   // 使用原始记录中的输赢金额
			Loss:             betReq.Loss,      // 使用原始记录中的输口
			AmountTip:        betReq.AmountTip, // 使用原始记录中的小费金额
			AmountBottom:     betReq.AmountBottom,
			WashRate:         betReq.WashRate,   // 使用原始记录中的洗码率
			WashAmount:       betReq.WashAmount, // 使用原始记录中的洗码量
			WashTip:          betReq.WashTip,    // 使用原始记录中的洗码费
			CreateTime:       "Redis记录",         // Redis记录没有具体创建时间
		}

		betRecords = append(betRecords, betRecord)

		// 按货币类型统计
		currencyName := s.getCurrencyTypeName(betReq.CurrencyType)
		currencyTotals[currencyName] += totalBetAmount
	}

	// 复制货币统计到按状态统计
	for currency, total := range currencyTotals {
		totalsByStatus[currency+"_下注总额"] = total
	}

	// 添加总体统计
	totalsByStatus["记录总数"] = float64(len(betRecords))
	totalsByStatus["总下注金额"] = 0
	for _, total := range currencyTotals {
		totalsByStatus["总下注金额"] += total
	}

	log.Printf("查询完成，找到 %d 条下注记录", len(betRecords))

	return &QueryBetRecordsResponse{
		Success:        true,
		Message:        fmt.Sprintf("成功找到 %d 条下注记录", len(betRecords)),
		TableID:        tableID,
		TableName:      table.TablesName,
		AccountPeriod:  req.AccountPeriod,
		RoundNo:        req.RoundNo,
		HandNo:         req.HandNo,
		RecordCount:    len(betRecords),
		BetRecords:     betRecords,
		TotalsByStatus: totalsByStatus,
	}, nil
}

// GetBetLimitsRequest 获取下注限制请求
type GetBetLimitsRequest struct {
	CurrencyType int8 `json:"currency_type" binding:"required"` // 货币类型:1-筹码;2-现金;3-U码
}

// BetAreaLimit 下注区域限制
type BetAreaLimit struct {
	BetArea     string  `json:"bet_area"`      // 下注区域
	Odds        float64 `json:"odds"`          // 赔率
	MaxBetU     float64 `json:"max_bet_u"`     // U码最大下注金额
	MaxBetCash  float64 `json:"max_bet_cash"`  // 现金最大下注金额
	MaxBetChips float64 `json:"max_bet_chips"` // 筹码最大下注金额
}

// GetBetLimitsResponse 获取下注限制响应
type GetBetLimitsResponse struct {
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	TableID      int64  `json:"table_id"`
	TableName    string `json:"table_name"`
	CurrencyType int8   `json:"currency_type"`
	// 桌台级别限制（当前账期全局限制）
	TableLimits struct {
		MaxBetU     float64 `json:"max_bet_u"`     // 桌台U码最大下注金额
		MaxBetCash  float64 `json:"max_bet_cash"`  // 桌台现金最大下注金额
		MaxBetChips float64 `json:"max_bet_chips"` // 桌台筹码最大下注金额
	} `json:"table_limits"`
	// 下注区域级别限制
	AreaLimits []BetAreaLimit `json:"area_limits"`
	// 当前货币类型的限制提示
	CurrentLimits struct {
		TableMaxAmount float64 `json:"table_max_amount"` // 桌台当前货币最大下注金额
		AreaLimits     []struct {
			BetArea   string  `json:"bet_area"`   // 下注区域
			MaxAmount float64 `json:"max_amount"` // 该区域该货币最大下注金额
		} `json:"area_limits"`
	} `json:"current_limits"`
}

// CheckBetLimitsRequest 检查下注限制请求
type CheckBetLimitsRequest struct {
	CurrencyType     int8    `json:"currency_type" binding:"required"` // 货币类型:1-筹码;2-现金;3-U码
	BankerAmount     float64 `json:"banker_amount"`                    // 庄-龙
	PlayerAmount     float64 `json:"player_amount"`                    // 闲-虎
	TieAmount        float64 `json:"tie_amount"`                       // 和
	BankerPairAmount float64 `json:"banker_pair_amount"`               // 庄对
	PlayerPairAmount float64 `json:"player_pair_amount"`               // 闲对
	Lucky6Amount     float64 `json:"lucky_6_amount"`                   // 幸运6
	Lucky7Amount     float64 `json:"lucky_7_amount"`                   // 幸运7
}

// ViolatedArea 违规区域信息
type ViolatedArea struct {
	BetArea    string  `json:"bet_area"`    // 下注区域
	BetAmount  float64 `json:"bet_amount"`  // 下注金额
	MaxAllowed float64 `json:"max_allowed"` // 最大允许金额
	ExceededBy float64 `json:"exceeded_by"` // 超出金额
	LimitType  string  `json:"limit_type"`  // 限制类型
}

// CheckBetLimitsResponse 检查下注限制响应
type CheckBetLimitsResponse struct {
	IsValid       bool           `json:"is_valid"`       // 是否有效
	ViolatedAreas []ViolatedArea `json:"violated_areas"` // 违规区域列表
	TableInfo     struct {
		TableID      int64  `json:"table_id"`      // 桌台ID
		TablesName   string `json:"tables_name"`   // 桌台名称
		CurrencyType int8   `json:"currency_type"` // 货币类型
		CurrencyName string `json:"currency_name"` // 货币名称
	} `json:"table_info"`
}

// GetBetLimits 获取桌台下注限制信息
func (s *BetService) GetBetLimits(req GetBetLimitsRequest, clientIP string) (*GetBetLimitsResponse, error) {
	// 参数验证
	if clientIP == "" {
		return nil, errors.New("客户端IP不能为空")
	}

	if req.CurrencyType < 1 || req.CurrencyType > 3 {
		return nil, errors.New("货币类型无效，应为1-筹码、2-现金、3-U码")
	}

	// 通过IP获取桌台信息
	tableService := NewTableService()
	tableID, err := tableService.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err = database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	// 获取桌台下注区域配置
	var tableBets []models.TableBet
	err = database.DB.Where("table_id = ?", tableID).Find(&tableBets).Error
	if err != nil {
		return nil, fmt.Errorf("获取桌台下注配置失败: %v", err)
	}

	// 构建响应数据
	response := &GetBetLimitsResponse{
		Success:      true,
		Message:      "获取下注限制成功",
		TableID:      tableID,
		TableName:    table.TablesName,
		CurrencyType: req.CurrencyType,
	}

	// 设置桌台级别限制
	response.TableLimits.MaxBetU = table.MaxBetU
	response.TableLimits.MaxBetCash = table.MaxBetCash
	response.TableLimits.MaxBetChips = table.MaxBetChips

	// 设置区域级别限制
	response.AreaLimits = make([]BetAreaLimit, len(tableBets))
	for i, bet := range tableBets {
		response.AreaLimits[i] = BetAreaLimit{
			BetArea:     bet.BetArea,
			Odds:        bet.Odds,
			MaxBetU:     bet.MaxBetU,
			MaxBetCash:  bet.MaxBetCash,
			MaxBetChips: bet.MaxBetChips,
		}
	}

	// 设置当前货币类型的限制提示
	switch req.CurrencyType {
	case 1: // 筹码
		response.CurrentLimits.TableMaxAmount = table.MaxBetChips
		response.CurrentLimits.AreaLimits = make([]struct {
			BetArea   string  `json:"bet_area"`
			MaxAmount float64 `json:"max_amount"`
		}, len(tableBets))
		for i, bet := range tableBets {
			response.CurrentLimits.AreaLimits[i].BetArea = bet.BetArea
			response.CurrentLimits.AreaLimits[i].MaxAmount = bet.MaxBetChips
		}
	case 2: // 现金
		response.CurrentLimits.TableMaxAmount = table.MaxBetCash
		response.CurrentLimits.AreaLimits = make([]struct {
			BetArea   string  `json:"bet_area"`
			MaxAmount float64 `json:"max_amount"`
		}, len(tableBets))
		for i, bet := range tableBets {
			response.CurrentLimits.AreaLimits[i].BetArea = bet.BetArea
			response.CurrentLimits.AreaLimits[i].MaxAmount = bet.MaxBetCash
		}
	case 3: // U码
		response.CurrentLimits.TableMaxAmount = table.MaxBetU
		response.CurrentLimits.AreaLimits = make([]struct {
			BetArea   string  `json:"bet_area"`
			MaxAmount float64 `json:"max_amount"`
		}, len(tableBets))
		for i, bet := range tableBets {
			response.CurrentLimits.AreaLimits[i].BetArea = bet.BetArea
			response.CurrentLimits.AreaLimits[i].MaxAmount = bet.MaxBetU
		}
	}

	return response, nil
}

// CheckBetLimits 检查下注限制
func (s *BetService) CheckBetLimits(req CheckBetLimitsRequest, clientIP string) (*CheckBetLimitsResponse, error) {
	// 参数验证
	if clientIP == "" {
		return nil, errors.New("客户端IP不能为空")
	}

	if req.CurrencyType < 1 || req.CurrencyType > 3 {
		return nil, errors.New("货币类型无效，应为1-筹码、2-现金、3-U码")
	}

	// 通过IP获取桌台信息
	tableService := NewTableService()
	tableID, err := tableService.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err = database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	// 获取桌台下注区域配置
	var tableBets []models.TableBet
	err = database.DB.Where("table_id = ?", tableID).Find(&tableBets).Error
	if err != nil {
		return nil, fmt.Errorf("获取桌台下注配置失败: %v", err)
	}

	// 构建区域限制映射
	areaLimits := make(map[string]float64)
	for _, bet := range tableBets {
		switch req.CurrencyType {
		case 1: // 筹码
			areaLimits[bet.BetArea] = bet.MaxBetChips
		case 2: // 现金
			areaLimits[bet.BetArea] = bet.MaxBetCash
		case 3: // U码
			areaLimits[bet.BetArea] = bet.MaxBetU
		}
	}

	// 检查各个下注区域
	var violatedAreas []ViolatedArea

	// 检查庄
	if req.BankerAmount > 0 {
		if maxAmount, exists := areaLimits["庄"]; exists && req.BankerAmount > maxAmount {
			violatedAreas = append(violatedAreas, ViolatedArea{
				BetArea:    "庄",
				BetAmount:  req.BankerAmount,
				MaxAllowed: maxAmount,
				ExceededBy: req.BankerAmount - maxAmount,
				LimitType:  "区域级别限制",
			})
		}
	}

	// 检查闲
	if req.PlayerAmount > 0 {
		if maxAmount, exists := areaLimits["闲"]; exists && req.PlayerAmount > maxAmount {
			violatedAreas = append(violatedAreas, ViolatedArea{
				BetArea:    "闲",
				BetAmount:  req.PlayerAmount,
				MaxAllowed: maxAmount,
				ExceededBy: req.PlayerAmount - maxAmount,
				LimitType:  "区域级别限制",
			})
		}
	}

	// 检查和
	if req.TieAmount > 0 {
		if maxAmount, exists := areaLimits["和"]; exists && req.TieAmount > maxAmount {
			violatedAreas = append(violatedAreas, ViolatedArea{
				BetArea:    "和",
				BetAmount:  req.TieAmount,
				MaxAllowed: maxAmount,
				ExceededBy: req.TieAmount - maxAmount,
				LimitType:  "区域级别限制",
			})
		}
	}

	// 检查庄对
	if req.BankerPairAmount > 0 {
		if maxAmount, exists := areaLimits["庄对"]; exists && req.BankerPairAmount > maxAmount {
			violatedAreas = append(violatedAreas, ViolatedArea{
				BetArea:    "庄对",
				BetAmount:  req.BankerPairAmount,
				MaxAllowed: maxAmount,
				ExceededBy: req.BankerPairAmount - maxAmount,
				LimitType:  "区域级别限制",
			})
		}
	}

	// 检查闲对
	if req.PlayerPairAmount > 0 {
		if maxAmount, exists := areaLimits["闲对"]; exists && req.PlayerPairAmount > maxAmount {
			violatedAreas = append(violatedAreas, ViolatedArea{
				BetArea:    "闲对",
				BetAmount:  req.PlayerPairAmount,
				MaxAllowed: maxAmount,
				ExceededBy: req.PlayerPairAmount - maxAmount,
				LimitType:  "区域级别限制",
			})
		}
	}

	// 检查幸运6
	if req.Lucky6Amount > 0 {
		if maxAmount, exists := areaLimits["幸运6"]; exists && req.Lucky6Amount > maxAmount {
			violatedAreas = append(violatedAreas, ViolatedArea{
				BetArea:    "幸运6",
				BetAmount:  req.Lucky6Amount,
				MaxAllowed: maxAmount,
				ExceededBy: req.Lucky6Amount - maxAmount,
				LimitType:  "区域级别限制",
			})
		}
	}

	// 检查幸运7
	if req.Lucky7Amount > 0 {
		if maxAmount, exists := areaLimits["幸运7"]; exists && req.Lucky7Amount > maxAmount {
			violatedAreas = append(violatedAreas, ViolatedArea{
				BetArea:    "幸运7",
				BetAmount:  req.Lucky7Amount,
				MaxAllowed: maxAmount,
				ExceededBy: req.Lucky7Amount - maxAmount,
				LimitType:  "区域级别限制",
			})
		}
	}

	// 检查桌台级别总限制
	totalBetAmount := req.BankerAmount + req.PlayerAmount + req.TieAmount +
		req.BankerPairAmount + req.PlayerPairAmount + req.Lucky6Amount + req.Lucky7Amount

	var tableMaxAmount float64
	switch req.CurrencyType {
	case 1: // 筹码
		tableMaxAmount = table.MaxBetChips
	case 2: // 现金
		tableMaxAmount = table.MaxBetCash
	case 3: // U码
		tableMaxAmount = table.MaxBetU
	}

	if totalBetAmount > tableMaxAmount {
		violatedAreas = append(violatedAreas, ViolatedArea{
			BetArea:    "总下注金额",
			BetAmount:  totalBetAmount,
			MaxAllowed: tableMaxAmount,
			ExceededBy: totalBetAmount - tableMaxAmount,
			LimitType:  "桌台级别限制",
		})
	}

	// 构建响应
	response := &CheckBetLimitsResponse{
		IsValid:       len(violatedAreas) == 0,
		ViolatedAreas: violatedAreas,
	}

	// 设置桌台信息
	response.TableInfo.TableID = tableID
	response.TableInfo.TablesName = table.TablesName
	response.TableInfo.CurrencyType = req.CurrencyType
	response.TableInfo.CurrencyName = s.getCurrencyTypeName(req.CurrencyType)

	return response, nil
}

// Redis键名管理相关常量和方法
const (
	// Redis键名前缀
	BetRecordKeyPrefix = "bet_record"

	// 键名分隔符
	KeySeparator = ":"
)

// generateUserBetKey 生成用户下注记录Redis键名
// 格式: {table_id}:{account_period}:{round_no}:{hand_no}:{wash_code}
func (s *BetService) generateUserBetKey(tableID int64, accountPeriod string, roundNo, handNo int, washCode string) string {
	return fmt.Sprintf("%d%s%s%s%d%s%d%s%s",
		tableID, KeySeparator,
		accountPeriod, KeySeparator,
		roundNo, KeySeparator,
		handNo, KeySeparator,
		washCode)
}

// generateUserBetPattern 生成用户下注记录查询模式
// 格式: {table_id}:{account_period}:{round_no}:{hand_no}:*
func (s *BetService) generateUserBetPattern(tableID int64, accountPeriod string, roundNo, handNo int) string {
	return fmt.Sprintf("%d%s%s%s%d%s%d%s*",
		tableID, KeySeparator,
		accountPeriod, KeySeparator,
		roundNo, KeySeparator,
		handNo, KeySeparator)
}

// generateBetRecordKey 生成下注记录Redis键名
// 格式: bet_record:{table_id}:{account_period}:{round_no}:{hand_no}:{wash_code}
func (s *BetService) generateBetRecordKey(tableID int64, accountPeriod string, roundNo, handNo int, washCode string) string {
	return fmt.Sprintf("%s%s%d%s%s%s%d%s%d%s%s",
		BetRecordKeyPrefix, KeySeparator,
		tableID, KeySeparator,
		accountPeriod, KeySeparator,
		roundNo, KeySeparator,
		handNo, KeySeparator,
		washCode)
}

// generateBetRecordPattern 生成下注记录查询模式
// 格式: bet_record:{table_id}:{account_period}:{round_no}:{hand_no}:*
func (s *BetService) generateBetRecordPattern(tableID int64, accountPeriod string, roundNo, handNo int) string {
	return fmt.Sprintf("%s%s%d%s%s%s%d%s%d%s*",
		BetRecordKeyPrefix, KeySeparator,
		tableID, KeySeparator,
		accountPeriod, KeySeparator,
		roundNo, KeySeparator,
		handNo, KeySeparator)
}

// validateRedisKey 验证Redis键名格式
func (s *BetService) validateRedisKey(key string) error {
	if key == "" {
		return errors.New("Redis键名不能为空")
	}

	if len(key) > 512 {
		return errors.New("Redis键名长度不能超过512字符")
	}

	// 检查是否包含非法字符
	if strings.Contains(key, " ") {
		return errors.New("Redis键名不能包含空格")
	}

	return nil
}

// 数据验证增强方法

// validateAccountPeriod 验证账期格式和有效性
func (s *BetService) validateAccountPeriod(accountPeriod string) error {
	if accountPeriod == "" {
		return errors.New("账期不能为空")
	}

	// 验证格式 YYYYMMDD
	if len(accountPeriod) != 8 {
		return errors.New("账期格式错误，应为8位数字(YYYYMMDD)")
	}

	// 验证是否为数字
	for _, char := range accountPeriod {
		if char < '0' || char > '9' {
			return errors.New("账期只能包含数字")
		}
	}

	// 解析日期验证有效性
	parsedTime, err := time.Parse("********", accountPeriod)
	if err != nil {
		return errors.New("账期格式错误，请使用有效的日期格式(YYYYMMDD)")
	}

	// 验证日期范围（不能是未来日期，不能太久远）
	now := time.Now()
	if parsedTime.After(now) {
		return errors.New("账期不能是未来日期")
	}

	// 不能超过1年前
	oneYearAgo := now.AddDate(-1, 0, 0)
	if parsedTime.Before(oneYearAgo) {
		return errors.New("账期不能超过一年前")
	}

	return nil
}

// validateWashCode 验证洗码号格式
func (s *BetService) validateWashCode(washCode string) error {
	if washCode == "" {
		return errors.New("洗码号不能为空")
	}

	if len(washCode) < 3 {
		return errors.New("洗码号长度不能少于3位")
	}

	if len(washCode) > 20 {
		return errors.New("洗码号长度不能超过20位")
	}

	// 检查是否包含非法字符（只允许字母、数字、下划线、连字符）
	for _, char := range washCode {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return errors.New("洗码号只能包含字母、数字、下划线和连字符")
		}
	}

	return nil
}

// validateBetAmounts 验证下注金额
func (s *BetService) validateBetAmounts(req BetRecordRequest) error {
	// 检查是否有负数
	amounts := []struct {
		name   string
		amount float64
	}{
		{"庄下注金额", req.BankerAmount},
		{"闲下注金额", req.PlayerAmount},
		{"和下注金额", req.TieAmount},
		{"庄对下注金额", req.BankerPairAmount},
		{"闲对下注金额", req.PlayerPairAmount},
		{"幸运6下注金额", req.Lucky6Amount},
		{"幸运7下注金额", req.Lucky7Amount},
		{"和底金额", req.AmountBottom},
	}

	for _, item := range amounts {
		if item.amount < 0 {
			return fmt.Errorf("%s不能为负数", item.name)
		}

		// 检查精度（最多2位小数）
		if item.amount*100 != float64(int(item.amount*100)) {
			return fmt.Errorf("%s最多只能有2位小数", item.name)
		}

		// 检查最大值限制（防止溢出）
		if item.amount > *********.99 {
			return fmt.Errorf("%s超出最大允许值", item.name)
		}
	}

	// 验证至少有一个下注金额大于0
	totalBetAmount := req.BankerAmount + req.PlayerAmount + req.TieAmount +
		req.BankerPairAmount + req.PlayerPairAmount + req.Lucky6Amount + req.Lucky7Amount

	if totalBetAmount <= 0 {
		return errors.New("至少需要一个下注金额大于0")
	}

	// 检查最小下注金额（例如不能小于1）
	minBetAmount := 1.0
	for _, item := range amounts[:7] { // 只检查下注金额，不包括和底
		if item.amount > 0 && item.amount < minBetAmount {
			return fmt.Errorf("%s不能小于%.2f", item.name, minBetAmount)
		}
	}

	return nil
}

// validateGameRounds 验证场次和局号
func (s *BetService) validateGameRounds(roundNo, handNo int) error {
	if roundNo <= 0 {
		return errors.New("场次编号必须大于0")
	}

	if roundNo > 9999 {
		return errors.New("场次编号不能超过9999")
	}

	if handNo <= 0 {
		return errors.New("局号编号必须大于0")
	}

	if handNo > 999 {
		return errors.New("局号编号不能超过999")
	}

	return nil
}

// validateUserName 验证用户名
func (s *BetService) validateUserName(userName string) error {
	if userName == "" {
		// 用户名可以为空，但如果不为空需要验证
		return nil
	}

	if len(userName) > 50 {
		return errors.New("用户名长度不能超过50个字符")
	}

	return nil
}

// 错误处理和日志记录增强方法

// logBetError 记录下注相关错误
func (s *BetService) logBetError(operation string, clientIP string, tableID int64, err error, details map[string]interface{}) {
	logData := map[string]interface{}{
		"operation": operation,
		"client_ip": clientIP,
		"table_id":  tableID,
		"error":     err.Error(),
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}

	// 合并详细信息
	for k, v := range details {
		logData[k] = v
	}

	// 转换为JSON格式记录
	if jsonData, jsonErr := json.Marshal(logData); jsonErr == nil {
		log.Printf("BET_ERROR: %s", string(jsonData))
	} else {
		log.Printf("BET_ERROR: %s - %v (JSON序列化失败: %v)", operation, err, jsonErr)
	}
}

// logBetSuccess 记录下注成功操作
func (s *BetService) logBetSuccess(operation string, clientIP string, tableID int64, details map[string]interface{}) {
	logData := map[string]interface{}{
		"operation": operation,
		"client_ip": clientIP,
		"table_id":  tableID,
		"status":    "success",
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}

	// 合并详细信息
	for k, v := range details {
		logData[k] = v
	}

	// 转换为JSON格式记录
	if jsonData, jsonErr := json.Marshal(logData); jsonErr == nil {
		log.Printf("BET_SUCCESS: %s", string(jsonData))
	} else {
		log.Printf("BET_SUCCESS: %s (JSON序列化失败: %v)", operation, jsonErr)
	}
}

// wrapError 包装错误信息，添加上下文
func (s *BetService) wrapError(operation string, originalErr error, context map[string]interface{}) error {
	if originalErr == nil {
		return nil
	}

	contextStr := ""
	if len(context) > 0 {
		if contextData, err := json.Marshal(context); err == nil {
			contextStr = fmt.Sprintf(" [上下文: %s]", string(contextData))
		}
	}

	return fmt.Errorf("%s失败: %v%s", operation, originalErr, contextStr)
}

// validateTableAccess 验证桌台访问权限
func (s *BetService) validateTableAccess(tableID int64, clientIP string) error {
	// 验证桌台是否存在且启用
	var table models.Table
	err := database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("桌台ID %d 不存在或未启用", tableID)
		}
		return fmt.Errorf("查询桌台信息失败: %v", err)
	}

	// 验证IP访问权限（如果桌台配置了IP限制）
	if table.TableIP != "" && table.TableIP != clientIP {
		return fmt.Errorf("客户端IP %s 无权访问桌台 %s (桌台IP: %s)", clientIP, table.TablesName, table.TableIP)
	}

	return nil
}

// sanitizeInput 清理输入数据
func (s *BetService) sanitizeInput(input string) string {
	// 移除前后空格
	input = strings.TrimSpace(input)

	// 移除控制字符
	var result strings.Builder
	for _, char := range input {
		if char >= 32 && char <= 126 {
			result.WriteRune(char)
		}
	}

	return result.String()
}

// CreateBatchBetRecord 批量下注记录保存
func (s *BetService) CreateBatchBetRecord(req BatchBetRequest, clientIP string) (*BatchBetResponse, error) {
	startTime := time.Now()

	// 参数验证
	if clientIP == "" {
		return nil, errors.New("客户端IP不能为空")
	}

	if len(req.BetRecords) == 0 {
		return nil, errors.New("下注记录不能为空")
	}

	if len(req.BetRecords) > 100 {
		return nil, errors.New("批量下注记录数量不能超过100条")
	}

	// 通过IP获取桌台ID
	tableService := NewTableService()
	tableID, err := tableService.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err = database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	ctx := context.Background()
	results := make([]SingleBetResult, 0, len(req.BetRecords))
	successCount := 0

	// 按洗码号分组处理
	washCodeGroups := make(map[string][]BetRecordRequest)
	washCodeIndexes := make(map[string][]int) // 记录每个洗码号对应的原始索引

	for idx, betRecord := range req.BetRecords {
		// 设置桌台ID
		betRecord.TableID = tableID

		// 基本验证
		if err := s.validateBetRecord(betRecord); err != nil {
			result := SingleBetResult{
				Index:        idx,
				WashCode:     betRecord.WashCode,
				CurrencyType: betRecord.CurrencyType,
				Success:      false,
				Message:      fmt.Sprintf("验证失败: %v", err),
			}
			results = append(results, result)
			continue
		}

		// 验证账期格式
		if _, err := time.Parse("********", betRecord.AccountPeriod); err != nil {
			result := SingleBetResult{
				Index:        idx,
				WashCode:     betRecord.WashCode,
				CurrencyType: betRecord.CurrencyType,
				Success:      false,
				Message:      fmt.Sprintf("账期格式错误: %v", err),
			}
			results = append(results, result)
			continue
		}

		// 按洗码号分组
		washCodeGroups[betRecord.WashCode] = append(washCodeGroups[betRecord.WashCode], betRecord)
		washCodeIndexes[betRecord.WashCode] = append(washCodeIndexes[betRecord.WashCode], idx)
	}

	// 处理每个洗码号的记录
	for washCode, records := range washCodeGroups {
		// 使用第一条记录的信息生成Redis键名
		firstRecord := records[0]
		redisKey := s.generateUserBetKey(tableID, firstRecord.AccountPeriod, firstRecord.RoundNo, firstRecord.HandNo, washCode)

		// 验证Redis键名
		if err := s.validateRedisKey(redisKey); err != nil {
			// 该洗码号的所有记录都失败
			for _, idx := range washCodeIndexes[washCode] {
				result := SingleBetResult{
					Index:        idx,
					WashCode:     washCode,
					CurrencyType: req.BetRecords[idx].CurrencyType,
					RedisKey:     redisKey,
					Success:      false,
					Message:      fmt.Sprintf("Redis键名验证失败: %v", err),
				}
				results = append(results, result)
			}
			continue
		}

		// 直接序列化整个记录数组（完全覆盖）
		recordsJSON, err := json.Marshal(records)
		if err != nil {
			// 该洗码号的所有记录都失败
			for _, idx := range washCodeIndexes[washCode] {
				result := SingleBetResult{
					Index:        idx,
					WashCode:     washCode,
					CurrencyType: req.BetRecords[idx].CurrencyType,
					RedisKey:     redisKey,
					Success:      false,
					Message:      fmt.Sprintf("序列化下注记录失败: %v", err),
				}
				results = append(results, result)
			}
			continue
		}

		// 保存到Redis，设置过期时间为24小时（完全覆盖）
		err = database.RedisClient.Set(ctx, redisKey, recordsJSON, 24*time.Hour).Err()
		if err != nil {
			// 该洗码号的所有记录都失败
			for _, idx := range washCodeIndexes[washCode] {
				result := SingleBetResult{
					Index:        idx,
					WashCode:     washCode,
					CurrencyType: req.BetRecords[idx].CurrencyType,
					RedisKey:     redisKey,
					Success:      false,
					Message:      fmt.Sprintf("保存到Redis失败: %v", err),
				}
				results = append(results, result)
			}
			continue
		}

		// 该洗码号的所有记录都成功
		for _, idx := range washCodeIndexes[washCode] {
			result := SingleBetResult{
				Index:        idx,
				WashCode:     washCode,
				CurrencyType: req.BetRecords[idx].CurrencyType,
				RedisKey:     redisKey,
				Success:      true,
				Message:      fmt.Sprintf("下注记录保存成功，洗码号: %s，货币类型: %d", washCode, req.BetRecords[idx].CurrencyType),
			}
			results = append(results, result)
			successCount++
		}
	}

	log.Printf("批量下注记录保存完成，桌台ID: %d, 总记录数: %d, 成功: %d, 失败: %d, 耗时: %v",
		tableID, len(req.BetRecords), successCount, len(req.BetRecords)-successCount, time.Since(startTime))

	// 构建响应
	response := &BatchBetResponse{
		Success:        successCount > 0,
		Message:        fmt.Sprintf("批量下注处理完成，成功: %d 条，失败: %d 条", successCount, len(req.BetRecords)-successCount),
		TableID:        tableID,
		ProcessedCount: len(req.BetRecords),
		Results:        results,
	}

	return response, nil
}

// getUserBetRecords 获取用户在Redis中的下注记录（兼容新旧格式）
func (s *BetService) getUserBetRecords(ctx context.Context, redisKey string) ([]BetRecordRequest, error) {
	// 尝试从Redis获取记录
	recordsJSON, err := database.RedisClient.Get(ctx, redisKey).Result()
	if err != nil {
		if err == redis.Nil {
			// 记录不存在，返回空数组
			return []BetRecordRequest{}, nil
		}
		return nil, fmt.Errorf("从Redis获取记录失败: %v", err)
	}

	// 先尝试解析为数组格式
	var records []BetRecordRequest
	err = json.Unmarshal([]byte(recordsJSON), &records)
	if err != nil {
		// 如果数组格式失败，尝试解析为单条记录格式
		var singleRecord BetRecordRequest
		err = json.Unmarshal([]byte(recordsJSON), &singleRecord)
		if err != nil {
			return nil, fmt.Errorf("反序列化记录失败: %v", err)
		}
		records = []BetRecordRequest{singleRecord}
	}

	return records, nil
}
