package services

import (
	"accounting_enter/internal/database"
	"accounting_enter/internal/models"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// FinanceService 财务服务
type FinanceService struct{}

// NewFinanceService 创建财务服务实例
func NewFinanceService() *FinanceService {
	return &FinanceService{}
}

// OutCodeRequest 出码申请请求结构
type OutCodeRequest struct {
	AccountPeriod string                 `json:"account_period" binding:"required"` // 账期 (格式: ********)
	OperationType int8                   `json:"operation_type" binding:"required"` // 操作类型:1-出码;3-加彩
	Amounts       []OutCodeAmountRequest `json:"amounts" binding:"required"`        // 各种货币类型的金额
}

// OutCodeAmountRequest 出码金额请求结构
type OutCodeAmountRequest struct {
	CurrencyType int8    `json:"currency_type" binding:"required"` // 货币类型:1-筹码;2-现金;3-U码
	TotalAmount  float64 `json:"total_amount" binding:"required"`  // 总金额
}

// OutCodeResponse 出码申请响应结构
type OutCodeResponse struct {
	ID            int     `json:"id"`
	TableID       int     `json:"table_id"`
	AccountPeriod string  `json:"account_period"`
	Type          int8    `json:"type"`
	TypeName      string  `json:"type_name"`
	Status        int8    `json:"status"`
	StatusName    string  `json:"status_name"`
	CurrencyType  int8    `json:"currency_type"`
	CurrencyName  string  `json:"currency_name"`
	TotalAmount   float64 `json:"total_amount"`
	Operator      string  `json:"operator"`
	Approver      string  `json:"approver"`
	CreateTime    string  `json:"create_time"`
}

// GetCodeSummaryRequest 点码查询请求结构体
type GetCodeSummaryRequest struct {
	AccountPeriod string `json:"account_period" binding:"required"` // 账期
	// 桌台ID将通过客户端IP自动获取
}

// CodeSummaryItem 点码查询单条数据
type CodeSummaryItem struct {
	CurrencyType int8    `json:"currency_type"` // 货币类型:1-筹码;2-现金;3-U码
	OutAmount    float64 `json:"out_amount"`    // 出码量
	BonusAmount  float64 `json:"bonus_amount"`  // 加彩
	TotalAmount  float64 `json:"total_amount"`  // 总码量(出码量+加彩-客人输赢)
	WinLoss      float64 `json:"win_loss"`      // 客人输赢
	TipAmount    float64 `json:"tip_amount"`    // 小费
	WaterAmount  float64 `json:"water_amount"`  // 码盘上下水
}

// GetCodeSummaryResponse 点码查询响应结构体
type GetCodeSummaryResponse struct {
	TableID       int64             `json:"table_id"`       // 桌台ID
	AccountPeriod string            `json:"account_period"` // 账期
	Items         []CodeSummaryItem `json:"items"`          // 按货币类型分组的数据
}

// ApplyOutCode 申请出码 - 已弃用，请使用BatchApplyOutCode
// 为了保持向后兼容，将单个申请转换为批量申请
func (s *FinanceService) ApplyOutCode(req OutCodeRequest, operator string, clientIP string) (*OutCodeResponse, error) {
	// 使用新的批量申请方法
	responses, err := s.BatchApplyOutCode(req, operator, clientIP)
	if err != nil {
		return nil, err
	}

	// 返回第一个响应（保持向后兼容）
	if len(responses) > 0 {
		return responses[0], nil
	}

	return nil, errors.New("未能创建出码申请")
}

// GetOutCodeList 获取出码申请列表
func (s *FinanceService) GetOutCodeList(tableID int, status int8, limit, offset int) ([]*OutCodeResponse, int64, error) {
	var records []models.FinanceRecord
	var total int64

	// 构建查询条件
	query := database.DB.Model(&models.FinanceRecord{}).Where("type = ?", models.FinanceTypeOut)

	// 添加桌台过滤
	if tableID > 0 {
		query = query.Where("table_id = ?", tableID)
	}

	// 添加状态过滤
	if status > 0 {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取出码申请总数失败: %v", err)
	}

	// 获取列表数据
	err = query.Order("create_time DESC").Limit(limit).Offset(offset).Find(&records).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取出码申请列表失败: %v", err)
	}

	// 格式化响应数据
	responses := make([]*OutCodeResponse, len(records))
	for i, record := range records {
		responses[i] = s.FormatFinanceResponse(&record)
	}

	return responses, total, nil
}

// GetOutCodeByID 根据ID获取出码申请详情
func (s *FinanceService) GetOutCodeByID(id int) (*OutCodeResponse, error) {
	var record models.FinanceRecord
	err := database.DB.Where("id = ? AND type = ?", id, models.FinanceTypeOut).First(&record).Error
	if err != nil {
		return nil, fmt.Errorf("未找到ID为 %d 的出码申请", id)
	}

	response := s.FormatFinanceResponse(&record)
	return response, nil
}

// FormatFinanceResponse 格式化财务记录响应数据
func (s *FinanceService) FormatFinanceResponse(record *models.FinanceRecord) *OutCodeResponse {
	return &OutCodeResponse{
		ID:            record.ID,
		TableID:       record.TableID,
		AccountPeriod: record.AccountPeriod,
		Type:          record.Type,
		TypeName:      s.getTypeName(record.Type),
		Status:        record.Status,
		StatusName:    s.getStatusName(record.Status),
		CurrencyType:  record.CurrencyType,
		CurrencyName:  s.getCurrencyName(record.CurrencyType),
		TotalAmount:   record.TotalAmount,
		Operator:      record.Operator,
		Approver:      record.Approver,
		CreateTime:    record.CreateTime.Format("2006-01-02 15:04:05"),
	}
}

// getTypeName 获取操作类型名称
func (s *FinanceService) getTypeName(operationType int8) string {
	switch operationType {
	case models.FinanceTypeOut:
		return "出码"
	case models.FinanceTypeIn:
		return "收码"
	case models.FinanceTypeBonus:
		return "加彩"
	default:
		return "未知"
	}
}

// getStatusName 获取状态名称
func (s *FinanceService) getStatusName(status int8) string {
	switch status {
	case 1:
		return "申请中"
	case 2:
		return "同意"
	case 3:
		return "拒绝"
	default:
		return "未知"
	}
}

// getCurrencyName 获取货币类型名称
func (s *FinanceService) getCurrencyName(currencyType int8) string {
	switch currencyType {
	case models.CurrencyTypeChips:
		return "筹码"
	case models.CurrencyTypeCash:
		return "现金"
	case models.CurrencyTypeUCode:
		return "U码"
	default:
		return "未知"
	}
}

// BatchApplyOutCode 批量申请出码
func (s *FinanceService) BatchApplyOutCode(req OutCodeRequest, operator string, clientIP string) ([]*OutCodeResponse, error) {
	// 参数验证
	if clientIP == "" {
		return nil, errors.New("客户端IP不能为空")
	}
	if req.AccountPeriod == "" {
		return nil, errors.New("账期不能为空")
	}
	if len(req.Amounts) == 0 {
		return nil, errors.New("申请金额列表不能为空")
	}
	if operator == "" {
		return nil, errors.New("操作人不能为空")
	}

	// 验证操作类型
	if req.OperationType != models.FinanceTypeOut && req.OperationType != models.FinanceTypeBonus {
		return nil, errors.New("操作类型无效，只支持出码(1)和加彩(3)")
	}

	// 通过IP获取桌台ID
	tableService := NewTableService()
	tableID, err := tableService.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	// 验证桌台是否存在且启用
	var table models.Table
	err = database.DB.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	// 验证账期格式
	if _, err := time.Parse("********", req.AccountPeriod); err != nil {
		return nil, errors.New("账期格式错误，请使用 YYYYMMDD 格式")
	}

	// 验证货币类型
	currencyMap := make(map[int8]bool)
	for _, amount := range req.Amounts {
		if amount.CurrencyType < 1 || amount.CurrencyType > 3 {
			return nil, errors.New("货币类型无效")
		}
		if amount.TotalAmount <= 0 {
			return nil, errors.New("申请金额必须大于0")
		}
		if currencyMap[amount.CurrencyType] {
			return nil, errors.New("不能重复申请同一种货币类型")
		}
		currencyMap[amount.CurrencyType] = true
	}

	// 验证唯一性：只有出码操作需要检查唯一性（每个桌台每个账期每个货币只能有一条同意的出码数据）
	if req.OperationType == models.FinanceTypeOut {
		for _, amount := range req.Amounts {
			var existingRecord models.FinanceRecord
			err := database.DB.Where("table_id = ? AND account_period = ? AND currency_type = ? AND type = ? AND status = ?",
				tableID, req.AccountPeriod, amount.CurrencyType, models.FinanceTypeOut, 2).First(&existingRecord).Error
			if err == nil {
				currencyName := s.getCurrencyName(amount.CurrencyType)
				return nil, fmt.Errorf("桌台%s在账期%s已存在%s的出码记录", table.TablesName, req.AccountPeriod, currencyName)
			}
		}
	}

	var responses []*OutCodeResponse

	// 开启数据库事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 根据操作类型设置状态和审核人
	var status int8
	var approver string

	if req.OperationType == models.FinanceTypeOut {
		// 出码操作：状态直接为同意
		status = 2
		approver = operator
	} else if req.OperationType == models.FinanceTypeBonus {
		// 加彩操作：状态为申请中
		status = 1
		approver = ""
	}

	// 批量创建申请记录
	for _, amount := range req.Amounts {
		record := models.FinanceRecord{
			TableID:       int(tableID),
			TablesName:    table.TablesName,
			AccountPeriod: req.AccountPeriod,
			Type:          req.OperationType, // 使用请求中的操作类型
			Status:        status,            // 根据操作类型设置状态
			CurrencyType:  amount.CurrencyType,
			TotalAmount:   amount.TotalAmount,
			Operator:      operator,
			Approver:      approver,
			CreateTime:    time.Now(),
		}

		// 保存到数据库
		err = tx.Create(&record).Error
		if err != nil {
			tx.Rollback()
			operationName := s.getTypeName(req.OperationType)
			return nil, fmt.Errorf("创建%s%s申请失败: %v", s.getCurrencyName(amount.CurrencyType), operationName, err)
		}

		// 构造响应
		response := s.FormatFinanceResponse(&record)
		responses = append(responses, response)
	}

	// 只有出码操作才需要更新tables_start状态为等待洗牌
	if req.OperationType == models.FinanceTypeOut {
		err = s.updateTablesStartStatus(tx, table.TablesName, req.AccountPeriod, 2)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("更新桌台状态失败: %v", err)
		}
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	return responses, nil
}

// updateTablesStartStatus 更新桌台账期状态
func (s *FinanceService) updateTablesStartStatus(tx *gorm.DB, tableCode, accountPeriod string, status int8) error {
	// 检查记录是否存在
	var tablesStart models.TablesStart
	err := tx.Where("table_id = (SELECT id FROM tables WHERE table_code = ?) AND account_period = ?",
		tableCode, accountPeriod).First(&tablesStart).Error

	if err != nil {
		// 如果记录不存在，创建新记录
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 获取桌台信息
			var table models.Table
			err = tx.Where("table_code = ?", tableCode).First(&table).Error
			if err != nil {
				return fmt.Errorf("获取桌台信息失败: %v", err)
			}

			newRecord := models.TablesStart{
				TableID:       int64(table.ID),
				TablesName:    table.TablesName,
				TableCode:     table.TableCode,
				AccountPeriod: accountPeriod,
				GameType:      table.GameType,
				Stats:         status, // 设置状态为等待洗牌
				CreateTime:    time.Now(),
			}

			return tx.Create(&newRecord).Error
		}
		return err
	}

	// 如果记录存在，更新状态
	return tx.Model(&tablesStart).Update("stats", status).Error
}

// GetCodeSummary 点码查询
func (s *FinanceService) GetCodeSummary(req GetCodeSummaryRequest, clientIP string) (*GetCodeSummaryResponse, error) {
	db := database.GetDB()

	// 通过IP获取桌台ID
	tableService := NewTableService()
	tableID, err := tableService.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	// 初始化3种货币类型的数据
	summaryMap := make(map[int8]*CodeSummaryItem)
	for i := int8(1); i <= 3; i++ {
		summaryMap[i] = &CodeSummaryItem{
			CurrencyType: i,
			OutAmount:    0,
			BonusAmount:  0,
			TotalAmount:  0,
			WinLoss:      0,
			TipAmount:    0,
			WaterAmount:  0,
		}
	}

	// 查询出码量 (type=1, status=2)
	var outRecords []models.FinanceRecord
	err = db.Where("table_id = ? AND account_period = ? AND type = ? AND status = ?",
		tableID, req.AccountPeriod, 1, 2).Find(&outRecords).Error
	if err != nil {
		return nil, err
	}

	// 统计出码量
	for _, record := range outRecords {
		if item, exists := summaryMap[record.CurrencyType]; exists {
			item.OutAmount += record.TotalAmount
		}
	}

	// 查询加彩 (type=3, status=2)
	var bonusRecords []models.FinanceRecord
	err = db.Where("table_id = ? AND account_period = ? AND type = ? AND status = ?",
		tableID, req.AccountPeriod, 3, 2).Find(&bonusRecords).Error
	if err != nil {
		return nil, err
	}

	// 统计加彩
	for _, record := range bonusRecords {
		if item, exists := summaryMap[record.CurrencyType]; exists {
			item.BonusAmount += record.TotalAmount
		}
	}

	// 查询客人输赢和小费 (hand_records)
	var handRecords []models.HandRecord
	err = db.Where("table_id = ? AND account_period = ?",
		tableID, req.AccountPeriod).Find(&handRecords).Error
	if err != nil {
		return nil, err
	}

	// 统计客人输赢和小费
	for _, record := range handRecords {
		if item, exists := summaryMap[record.CurrencyType]; exists {
			item.WinLoss += record.WinLoss
			item.TipAmount += record.AmountTip
		}
	}

	// 计算总码量和码盘上下水
	for _, item := range summaryMap {
		// 总码量 = 出码量 + 加彩 - 客人输赢
		item.TotalAmount = item.OutAmount + item.BonusAmount - item.WinLoss
		// 码盘上下水 = 总码量 - 小费
		item.WaterAmount = item.TotalAmount - item.TipAmount
	}

	// 转换为数组
	var items []CodeSummaryItem
	for _, item := range summaryMap {
		items = append(items, *item)
	}

	response := &GetCodeSummaryResponse{
		TableID:       tableID,
		AccountPeriod: req.AccountPeriod,
		Items:         items,
	}

	return response, nil
}

// BatchSettlementRequest 批量点码请求结构体
type BatchSettlementRequest struct {
	SettlementRecords []SettlementRecordRequest `json:"settlement_records" binding:"required,min=1,dive"`
}

// SettlementRecordRequest 点码记录请求结构体
type SettlementRecordRequest struct {
	ShoeNo        int     `json:"shoe_no" binding:"required"`        // 场次编号
	AccountPeriod string  `json:"account_period" binding:"required"` // 账期
	ChipsOutput   float64 `json:"chips_output" binding:"required"`   // 出码量
	ChipsAdd      float64 `json:"chips_add" binding:"required"`      // 加彩量
	CurrencyType  int8    `json:"currency_type" binding:"required"`  // 货币类型:1-筹码;2-现金;3-U码
	Chips20W      int     `json:"chips_20W"`                         // 筹码20万
	Chips10W      int     `json:"chips_10W"`                         // 筹码10万
	Chips5W       int     `json:"chips_5W"`                          // 筹码5万
	Chips1W       int     `json:"chips_1W"`                          // 筹码1万
	Chips5K       int     `json:"chips_5k"`                          // 筹码5千
	Chips1K       int     `json:"chips_1k"`                          // 筹码1千
	Chips500      int     `json:"chips_500"`                         // 筹码500
	Chips100      int     `json:"chips_100"`                         // 筹码100
	Chips50       int     `json:"chips_50"`                          // 筹码50
	Chips10       int     `json:"chips_10"`                          // 筹码10
	Chips5        int     `json:"chips_5"`                           // 筹码5
	TotalAmount   float64 `json:"total_amount"`                      // 总额
	ClientWinLoss float64 `json:"client_win_loss"`                   // 客户输赢总和
	AmountTip     float64 `json:"amount_tip"`                        // 小费金额
	AmountBottom  float64 `json:"amount_bottom"`                     // 和底
	WashRate      float64 `json:"wash_rate"`                         // 洗码率
	WashAmount    float64 `json:"wash_amount"`                       // 本局洗码量
	WashTip       float64 `json:"wash_tip"`                          // 洗码费
	CompareResult string  `json:"compare_result"`                    // 点码与注单比对结果
	Remark        string  `json:"remark"`                            // 备注
	Operator      string  `json:"operator"`                          // 操作人员
}

// BatchSettlementResponse 批量点码响应结构体
type BatchSettlementResponse struct {
	Message           string                      `json:"message"`
	SettlementRecords []*SettlementRecordResponse `json:"settlement_records"`
}

// SettlementRecordResponse 点码记录响应结构体
type SettlementRecordResponse struct {
	ID            int     `json:"id"`
	TableID       int64   `json:"table_id"`
	ShoeNo        int     `json:"shoe_no"`
	AccountPeriod string  `json:"account_period"`
	CurrencyType  int8    `json:"currency_type"`
	Chips20W      int     `json:"chips_20w"`
	Chips10W      int     `json:"chips_10w"`
	Chips5W       int     `json:"chips_5w"`
	Chips1W       int     `json:"chips_1w"`
	Chips5K       int     `json:"chips_5k"`
	Chips1K       int     `json:"chips_1k"`
	Chips500      int     `json:"chips_500"`
	Chips100      int     `json:"chips_100"`
	Chips50       int     `json:"chips_50"`
	Chips10       int     `json:"chips_10"`
	Chips5        int     `json:"chips_5"`
	TotalAmount   float64 `json:"total_amount"`
	ClientWinLoss float64 `json:"client_win_loss"`
	AmountTip     float64 `json:"amount_tip"`
	AmountBottom  float64 `json:"amount_bottom"`
	WashRate      float64 `json:"wash_rate"`
	WashAmount    float64 `json:"wash_amount"`
	WashTip       float64 `json:"wash_tip"`
	CompareResult string  `json:"compare_result"`
	Remark        string  `json:"remark"`
	Operator      string  `json:"operator"`
	CreateTime    string  `json:"create_time"`
}

// BatchSettlement 批量点码
func (s *FinanceService) BatchSettlement(req BatchSettlementRequest, clientIP string) (*BatchSettlementResponse, error) {
	db := database.GetDB()

	if len(req.SettlementRecords) == 0 {
		return nil, errors.New("点码记录不能为空")
	}

	// 通过IP获取桌台ID
	tableService := NewTableService()
	tableID, err := tableService.GetTableIDByIP(clientIP)
	if err != nil {
		return nil, fmt.Errorf("获取桌台信息失败: %v", err)
	}

	// 获取桌台信息
	var table models.Table
	err = db.Where("id = ? AND status = ?", tableID, 2).First(&table).Error
	if err != nil {
		return nil, errors.New("桌台不存在或未启用")
	}

	// 验证所有记录
	for i, record := range req.SettlementRecords {
		if err := s.validateSettlementRecord(record); err != nil {
			return nil, fmt.Errorf("第%d条记录验证失败: %v", i+1, err)
		}
	}

	// 检查唯一性约束
	for _, record := range req.SettlementRecords {
		var existingRecord models.SettlementRecord
		err = db.Where("table_id = ? AND shoe_no = ? AND account_period = ? AND currency_type = ?",
			tableID, record.ShoeNo, record.AccountPeriod, record.CurrencyType).First(&existingRecord).Error
		if err == nil {
			return nil, fmt.Errorf("桌台%d场次%d账期%s货币类型%d已存在点码记录",
				tableID, record.ShoeNo, record.AccountPeriod, record.CurrencyType)
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("检查唯一性失败: %v", err)
		}
	}

	// 开始事务
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var createdRecords []models.SettlementRecord
	var responseRecords []*SettlementRecordResponse

	// 批量保存点码记录
	for _, settlementReq := range req.SettlementRecords {
		settlementRecord := models.SettlementRecord{
			TableID:       int(tableID),
			TablesName:    table.TablesName,
			ShoeNo:        settlementReq.ShoeNo,
			AccountPeriod: settlementReq.AccountPeriod,
			ChipsOutput:   settlementReq.ChipsOutput,
			ChipsAdd:      settlementReq.ChipsAdd,
			CurrencyType:  settlementReq.CurrencyType,
			Chips20W:      settlementReq.Chips20W,
			Chips10W:      settlementReq.Chips10W,
			Chips5W:       settlementReq.Chips5W,
			Chips1W:       settlementReq.Chips1W,
			Chips5K:       settlementReq.Chips5K,
			Chips1K:       settlementReq.Chips1K,
			Chips500:      settlementReq.Chips500,
			Chips100:      settlementReq.Chips100,
			Chips50:       settlementReq.Chips50,
			Chips10:       settlementReq.Chips10,
			Chips5:        settlementReq.Chips5,
			TotalAmount:   settlementReq.TotalAmount,
			ClientWinLoss: settlementReq.ClientWinLoss,
			AmountTip:     settlementReq.AmountTip,
			AmountBottom:  settlementReq.AmountBottom,
			WashRate:      settlementReq.WashRate,
			WashAmount:    settlementReq.WashAmount,
			WashTip:       settlementReq.WashTip,
			CompareResult: settlementReq.CompareResult,
			Remark:        settlementReq.Remark,
			Operator:      settlementReq.Operator,
		}

		err := tx.Create(&settlementRecord).Error
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("保存点码记录失败: %v", err)
		}

		createdRecords = append(createdRecords, settlementRecord)
		responseRecords = append(responseRecords, s.formatSettlementRecordResponse(&settlementRecord))
	}

	// 更新TablesStart状态为等待洗牌
	if len(createdRecords) > 0 {
		firstRecord := createdRecords[0]
		err := tx.Model(&models.TablesStart{}).Where("table_id = ? AND account_period = ?",
			firstRecord.TableID, firstRecord.AccountPeriod).Update("stats", 2).Error // 2-等待洗牌
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("更新桌台状态失败: %v", err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	response := &BatchSettlementResponse{
		Message:           "批量点码成功",
		SettlementRecords: responseRecords,
	}

	return response, nil
}

// validateSettlementRecord 验证单条点码记录
func (s *FinanceService) validateSettlementRecord(req SettlementRecordRequest) error {
	if req.ShoeNo <= 0 {
		return errors.New("场次编号不能为空")
	}
	if req.AccountPeriod == "" {
		return errors.New("账期不能为空")
	}
	if req.CurrencyType < 1 || req.CurrencyType > 3 {
		return errors.New("货币类型无效")
	}
	if req.TotalAmount < 0 {
		return errors.New("总额不能为负数")
	}
	return nil
}

// formatSettlementRecordResponse 格式化点码记录响应数据
func (s *FinanceService) formatSettlementRecordResponse(record *models.SettlementRecord) *SettlementRecordResponse {
	return &SettlementRecordResponse{
		ID:            record.ID,
		TableID:       int64(record.TableID),
		ShoeNo:        record.ShoeNo,
		AccountPeriod: record.AccountPeriod,
		CurrencyType:  record.CurrencyType,
		Chips20W:      record.Chips20W,
		Chips10W:      record.Chips10W,
		Chips5W:       record.Chips5W,
		Chips1W:       record.Chips1W,
		Chips5K:       record.Chips5K,
		Chips1K:       record.Chips1K,
		Chips500:      record.Chips500,
		Chips100:      record.Chips100,
		Chips50:       record.Chips50,
		Chips10:       record.Chips10,
		Chips5:        record.Chips5,
		TotalAmount:   record.TotalAmount,
		ClientWinLoss: record.ClientWinLoss,
		AmountTip:     record.AmountTip,
		AmountBottom:  record.AmountBottom,
		WashRate:      record.WashRate,
		WashAmount:    record.WashAmount,
		WashTip:       record.WashTip,
		CompareResult: record.CompareResult,
		Remark:        record.Remark,
		Operator:      record.Operator,
		CreateTime:    record.CreateTime.Format("2006-01-02 15:04:05"),
	}
}

// parseDate 解析日期字符串
func parseDate(dateStr string) time.Time {
	// 尝试多种日期格式
	formats := []string{"2006-01-02", "********", "2006/01/02"}
	for _, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return t
		}
	}
	// 如果都解析失败，返回当前时间
	return time.Now()
}
