# 收盘展示数据接口说明

## 接口概述

新增的收盘展示数据接口用于获取桌台当前账期最新的点码数据，按货币类型区分，返回货币类型、总额、点码与注单比对结果。

## 接口详情

### WebSocket消息类型
```
get_settlement_display
```

### 请求参数
```json
{
    "type": "get_settlement_display",
    "data": {
        "account_period": "********"  // 账期，YYYYMMDD格式
    }
}
```

### 响应格式

#### 成功响应
```json
{
    "type": "get_settlement_display_success",
    "from": "server",
    "to": "client_id",
    "data": {
        "message": "获取收盘展示数据成功",
        "data": [
            {
                "currency_type": 1,           // 货币类型：1-筹码，2-现金，3-U码
                "total_amount": 670000.00,    // 总额
                "compare_result": "点码与注单一致（更新）"  // 点码与注单比对结果
            },
            {
                "currency_type": 2,
                "total_amount": 350000.00,
                "compare_result": "点码多于注单 +25000"
            },
            {
                "currency_type": 3,
                "total_amount": 230000.00,
                "compare_result": "点码少于注单 -15000"
            }
        ]
    },
    "timestamp": *************
}
```

#### 错误响应
```json
{
    "type": "get_settlement_display_error",
    "from": "server",
    "to": "client_id",
    "data": {
        "error": "错误信息",
        "client_ip": "*************"
    },
    "timestamp": *************
}
```

## 业务逻辑

1. **桌台识别**：通过客户端IP地址自动识别桌台
2. **数据查询**：查询指定账期的settlement_records表数据
3. **去重逻辑**：每种货币类型只返回最新的一条记录（按create_time排序）
4. **默认处理**：如果某种货币类型没有数据，返回默认值（总额0.00，比对结果"暂无数据"）
5. **数据完整性**：始终返回3种货币类型的数据（筹码、现金、U码）

## 数据库表结构

接口查询的是 `settlement_records` 表，主要字段：
- `table_id`: 桌台ID
- `account_period`: 账期
- `currency_type`: 货币类型（1-筹码，2-现金，3-U码）
- `total_amount`: 总额
- `compare_result`: 点码与注单比对结果
- `create_time`: 创建时间

## 测试方法

### 1. 准备测试数据
```sql
-- 执行测试数据插入脚本
source insert_settlement_test_data.sql;
```

### 2. 使用测试页面
1. 打开 `http://************:8080/static/test_settlement_display.html`
2. 连接WebSocket服务器
3. 登录系统
4. 输入账期（如：********）
5. 点击"获取收盘展示数据"按钮

### 3. 预期结果
- 返回3条记录，分别对应筹码、现金、U码
- 每条记录包含货币类型、总额、比对结果
- 数据按货币类型排序显示

## 错误处理

常见错误情况：
1. **用户未登录**：返回"用户未登录"错误
2. **令牌无效**：返回"令牌无效，请重新登录"错误
3. **桌台不存在**：返回"桌台不存在或未启用"错误
4. **账期格式错误**：返回"账期格式错误，请使用 YYYYMMDD 格式"错误
5. **数据库查询失败**：返回具体的数据库错误信息

## 使用场景

此接口主要用于：
1. **收盘界面**：显示当前账期各货币类型的点码汇总
2. **数据核对**：展示点码与注单的比对结果
3. **财务统计**：提供收盘时的关键财务数据

## 注意事项

1. **权限要求**：需要用户登录并验证令牌
2. **IP限制**：只能查询当前客户端IP对应桌台的数据
3. **数据实时性**：返回的是数据库中的最新记录
4. **货币类型完整性**：即使某种货币类型没有数据，也会返回默认记录
