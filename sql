CREATE TABLE bet_records (
    id bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    table_id bigint NOT NULL COMMENT '桌台ID',
    game_type tinyint(1) NOT NULL COMMENT '游戏类型:1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;-轮盘;',
    account_period varchar(32) NOT NULL DEFAULT '' COMMENT '账期',
    round_no int NOT NULL COMMENT '场次编号',
    hand_no int NOT NULL COMMENT '局号编号',
    wash_code varchar(32) NOT NULL COMMENT '洗码号，用于识别客户身份',
    user_name varchar(64) NULL COMMENT '客户姓名',
    currency_type tinyint(1) NULL COMMENT '货币类型:1-筹码;2-现金;3-U码;',
    banker_amount decimal(12, 2) NULL COMMENT '庄-龙',
    player_amount decimal(12, 2) NULL COMMENT '闲-虎',
    tie_amount decimal(12, 2) NULL COMMENT '和',
    banker_pair_amount decimal(12, 2) NULL COMMENT '庄对',
    player_pair_amount decimal(12, 2) NULL COMMENT '闲对',
    lucky_6_amount decimal(12, 2) NULL COMMENT '幸运6',
    lucky_7_amount decimal(12, 2) NULL COMMENT '幸运7',
    win_result char(12) NULL COMMENT '结果,通过,分割',
    win_loss decimal(12, 2) NULL COMMENT '输赢金额（正数为赢，负数为输）',
    loss decimal(12, 2) NULL COMMENT '输口',
    amount_tip decimal(12, 2) NULL DEFAULT 0.00 COMMENT '小费金额',
    amount_bottom decimal(12, 2) NULL DEFAULT 0.00 COMMENT '和底',
    wash_rate decimal(5, 4) NULL COMMENT '洗码率',
    wash_amount decimal(12, 2) NULL DEFAULT 0.00 COMMENT '本局洗码量',
    wash_tip decimal(12, 2) NULL DEFAULT 0.00 COMMENT '洗码费',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    CONSTRAINT wash_code UNIQUE wash_code (wash_code)
) COMMENT '下注记录';

CREATE TABLE finance_records (
    id int PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    table_id int NOT NULL COMMENT '桌台ID',
    account_period date NOT NULL COMMENT '账期',
    type tinyint(1) NOT NULL COMMENT '操作类型:1-出码;2-收码;3-加彩;',
    status tinyint(1) NOT NULL COMMENT '状态:1-申请中;2-同意;3-拒绝;',
    currency_type tinyint(1) NULL COMMENT '货币类型:1-筹码;2-现金;3-U码;',
    total_amount decimal(12, 2) NULL COMMENT '总金额',
    operator varchar(64) NULL COMMENT '操作人',
    approver varchar(64) NULL COMMENT '审核人',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '出码/收码记录';

CREATE TABLE hand_records (
    id bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    table_id bigint NOT NULL COMMENT '桌台ID',
    account_period varchar(32) NOT NULL DEFAULT '' COMMENT '账期',
    shoe_no int NOT NULL COMMENT '场次编号',
    hand_no int NOT NULL COMMENT '局号编号',
    game_type tinyint(1) NULL COMMENT '游戏类型:1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;-轮盘;',
    currency_type tinyint(1) NULL COMMENT '货币类型:1-筹码;2-现金;3-U码;',
    bet_amount decimal(12, 2) NOT NULL COMMENT '下注金额',
    result_1 varchar(64) NULL COMMENT '结果区域（庄、闲等）',
    win_loss decimal(12, 2) NULL COMMENT '输赢金额（正数为赢，负数为输）',
    loss decimal(12, 2) NULL COMMENT '输口',
    amount_tip decimal(12, 2) NULL DEFAULT 0.00 COMMENT '小费金额',
    amount_bottom decimal(12, 2) NULL DEFAULT 0.00 COMMENT '和底',
    wash_rate decimal(5, 4) NULL COMMENT '洗码率',
    wash_amount decimal(12, 2) NULL DEFAULT 0.00 COMMENT '本局洗码量',
    wash_tip decimal(12, 2) NULL DEFAULT 0.00 COMMENT '洗码费',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '条口记录';

CREATE TABLE login_logs (
    id bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    sys_user_id bigint NULL COMMENT '用户ID',
    sys_user_name varchar(64) NULL COMMENT '用户名',
    ip_address varchar(64) NULL COMMENT '登录IP地址',
    login_type tinyint(1) NULL COMMENT '登录类型:1-登录;2-登出;3-交接班;',
    device_info varchar(128) NULL COMMENT '设备信息或浏览器',
    message varchar(255) NULL COMMENT '登录结果说明',
    login_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间'
) COMMENT '系统登录日志';

CREATE TABLE settlement_records (
    id int PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    table_id int NOT NULL COMMENT '桌台ID',
    shoe_no int NOT NULL COMMENT '场次编号',
    account_period date NOT NULL COMMENT '账期',
    chips_output decimal(12, 2) NULL DEFAULT 0.00 COMMENT '出码量',
    chips_add decimal(12, 2) NULL DEFAULT 0.00 COMMENT '加彩量',
    currency_type tinyint(1) NULL COMMENT '货币类型:1-筹码;2-现金;3-U码;',
    chips_20W int NOT NULL DEFAULT 0 COMMENT '筹码20万',
    chips_10W int NOT NULL DEFAULT 0 COMMENT '筹码10万',
    chips_5W int NOT NULL DEFAULT 0 COMMENT '筹码5万',
    chips_1W int NOT NULL DEFAULT 0 COMMENT '筹码1万',
    chips_5K int NOT NULL DEFAULT 0 COMMENT '筹码5千',
    chips_1K int NOT NULL DEFAULT 0 COMMENT '筹码1千',
    chips_500 int NOT NULL DEFAULT 0 COMMENT '筹码500',
    chips_100 int NOT NULL DEFAULT 0 COMMENT '筹码100',
    chips_50 int NOT NULL DEFAULT 0 COMMENT '筹码50',
    chips_10 int NOT NULL DEFAULT 0 COMMENT '筹码10',
    chips_5 int NOT NULL DEFAULT 0 COMMENT '筹码5',
    total_amount decimal(12, 2) NULL DEFAULT 0.00 COMMENT '总额',
    client_win_loss decimal(12, 2) NULL DEFAULT 0.00 COMMENT '客户输赢总和',
    amount_tip decimal(12, 2) NULL DEFAULT 0.00 COMMENT '小费金额',
    amount_bottom decimal(12, 2) NULL DEFAULT 0.00 COMMENT '和底',
    wash_rate decimal(5, 4) NULL COMMENT '洗码率',
    wash_amount decimal(12, 2) NULL DEFAULT 0.00 COMMENT '本局洗码量',
    wash_tip decimal(12, 2) NULL DEFAULT 0.00 COMMENT '洗码费',
    compare_result varchar(255) NULL COMMENT '点码与注单比对结果',
    remark text NULL COMMENT '备注',
    operator varchar(64) NULL COMMENT '操作人员',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '点码记录';

CREATE TABLE shuffle_records (
    id bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    table_id bigint NOT NULL COMMENT '桌台ID',
    account_period date NOT NULL COMMENT '账期',
    shoe_no int NOT NULL COMMENT '场次编号',
    card_no int NOT NULL COMMENT '牌次号',
    shift tinyint(1) NOT NULL COMMENT '班次:1-早班;2-晚班;',
    shuffle_method varchar(64) NULL COMMENT '洗牌方式',
    card_color varchar(64) NULL COMMENT '牌色',
    monitor_id varchar(64) NULL COMMENT '监场人员ID',
    monitor_name varchar(64) NULL COMMENT '监场人员名称',
    admin_id varchar(64) NULL COMMENT '管理员ID',
    admin_name varchar(64) NULL COMMENT '管理员名称',
    shuffle_table_poker varchar(64) NULL COMMENT '洗牌卓牌手',
    table_poker varchar(64) NULL COMMENT '台面洗牌牌手',
    monitor_poker varchar(64) NULL COMMENT '监管洗牌手',
    cut_card_dealer varchar(64) NULL COMMENT '切牌人',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '洗牌记录';

CREATE TABLE sys_menu (
    id int PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    parent_id int NOT NULL DEFAULT 0 COMMENT '父级ID',
    title varchar(30) NOT NULL COMMENT '菜单标题',
    icon varchar(50) NULL COMMENT '图标',
    path varchar(150) NULL COMMENT '菜单路径',
    component varchar(150) NULL COMMENT '菜单组件',
    target varchar(30) NULL COMMENT '打开方式：0组件 1内链 2外链',
    permission varchar(150) NULL COMMENT '权限标识',
    type tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型：0菜单 1节点',
    status tinyint(1) NULL DEFAULT 1 COMMENT '状态：1正常 2禁用',
    hide tinyint(1) NULL DEFAULT 1 COMMENT '是否可见：1是 2否',
    note varchar(255) NULL COMMENT '备注',
    sort smallint NULL DEFAULT 125 COMMENT '显示顺序',
    create_user int NULL DEFAULT 0 COMMENT '添加人',
    create_time int UNSIGNED NULL DEFAULT '0' COMMENT '创建时间',
    update_user int NULL DEFAULT 0 COMMENT '更新人',
    update_time int UNSIGNED NULL COMMENT '更新时间',
    mark tinyint(1) NOT NULL DEFAULT 1 COMMENT '有效标识'
) ROW_FORMAT = COMPACT COMMENT '系统菜单表';

CREATE INDEX index_name ON sys_menu (title);

CREATE INDEX index_pid ON sys_menu (parent_id);

CREATE TABLE sys_role (
    id int PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name varchar(150) NOT NULL COMMENT '角色名称',
    code varchar(100) CHARSET utf8mb3  NOT NULL COMMENT '角色标签',
    note varchar(255) NULL COMMENT '备注',
    sort smallint NOT NULL DEFAULT 125 COMMENT '排序',
    status tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1正常 2停用',
    create_user int NOT NULL DEFAULT 0 COMMENT '添加人',
    create_time int UNSIGNED NOT NULL DEFAULT '0' COMMENT '添加时间',
    update_user int NULL DEFAULT 0 COMMENT '更新人',
    update_time int UNSIGNED NULL DEFAULT '0' COMMENT '更新时间',
    mark tinyint(1) NOT NULL DEFAULT 1 COMMENT '有效标识'
) ROW_FORMAT = COMPACT COMMENT '系统角色表';

CREATE INDEX name ON sys_role (name);

CREATE TABLE sys_role_menu (
    role_id smallint NOT NULL DEFAULT 0 COMMENT '角色ID',
    menu_id smallint NOT NULL DEFAULT 0 COMMENT '菜单ID'
) ROW_FORMAT = COMPACT COMMENT '角色菜单关联表';

CREATE INDEX role_id ON sys_role_menu (menu_id);

CREATE TABLE sys_user (
    id int PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    serial_number int NOT NULL COMMENT '员工编号',
    realname varchar(150) NULL COMMENT '真实姓名',
    nickname varchar(150) NULL COMMENT '昵称',
    gender tinyint(1) NULL DEFAULT 3 COMMENT '性别:1男 2女 3保密',
    avatar varchar(150) NULL COMMENT '头像',
    mobile char(11) NULL COMMENT '手机号码',
    email varchar(30) NULL COMMENT '邮箱地址',
    birthday int UNSIGNED NULL DEFAULT '0' COMMENT '出生日期',
    dept_id int NULL DEFAULT 0 COMMENT '部门ID',
    level_id int NULL DEFAULT 0 COMMENT '职级ID',
    position_id smallint NULL DEFAULT 0 COMMENT '岗位ID',
    province_code varchar(50) NULL COMMENT '省份编号',
    city_code varchar(50) NULL COMMENT '市区编号',
    district_code varchar(50) NULL COMMENT '区县编号',
    address varchar(255) NULL COMMENT '详细地址',
    city_name varchar(150) NULL COMMENT '所属城市',
    username varchar(50) NULL COMMENT '登录用户名',
    password varchar(150) NULL COMMENT '登录密码',
    salt varchar(30) NULL COMMENT '盐加密',
    intro varchar(500) NULL COMMENT '个人简介',
    status tinyint(1) NULL DEFAULT 1 COMMENT '状态：1正常 2禁用',
    note varchar(500) NULL COMMENT '备注',
    sort int NULL DEFAULT 125 COMMENT '排序号',
    login_num int NULL DEFAULT 0 COMMENT '登录次数',
    login_ip varchar(20) NULL COMMENT '最近登录IP',
    login_time int UNSIGNED NULL DEFAULT '0' COMMENT '最近登录时间',
    create_user int NULL DEFAULT 0 COMMENT '添加人',
    create_time int UNSIGNED NULL DEFAULT '0' COMMENT '创建时间',
    update_user int NULL DEFAULT 0 COMMENT '更新人',
    update_time int UNSIGNED NULL DEFAULT '0' COMMENT '更新时间',
    mark tinyint(1) NOT NULL DEFAULT 1 COMMENT '有效标识(1正常 0删除)'
) ROW_FORMAT = COMPACT COMMENT '后台用户管理表';

CREATE INDEX realname ON sys_user (realname);

CREATE TABLE sys_user_role (
    user_id int NOT NULL DEFAULT 0 COMMENT '人员ID',
    role_id int NOT NULL DEFAULT 0 COMMENT '角色ID'
) ROW_FORMAT = COMPACT COMMENT '人员角色表';

CREATE INDEX admin_id ON sys_user_role (user_id);

CREATE TABLE tables (
    id int PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    table_code varchar(32) NOT NULL COMMENT '桌台编号',
    table_name varchar(64) NULL COMMENT '桌台名称',
    game_type tinyint(1) NULL COMMENT '游戏类型:1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;-轮盘;',
    table_ip varchar(20) NULL COMMENT '桌台IP',
    video_url varchar(20) NULL COMMENT '视频地址',
    channel tinyint(1) NOT NULL DEFAULT 1 COMMENT '桌台所属业务通道:1-现场;2-电投;3-网投;',
    wash_user_type char(10) NOT NULL DEFAULT '' COMMENT '参与洗码用户类型,多选',
    wash_rate decimal(5, 4) NULL COMMENT '洗码率',
    max_bet_u decimal(12, 2) NULL DEFAULT 0.00 COMMENT 'U码最大下注金额',
    max_bet_cash decimal(12, 2) NULL DEFAULT 0.00 COMMENT '现金最大下注金额',
    max_bet_chips decimal(12, 2) NULL DEFAULT 0.00 COMMENT '筹码最大下注金额',
    tie_rate decimal(5, 4) NOT NULL COMMENT '龙虎和底返',
    status tinyint(1) NULL DEFAULT 1 COMMENT '状态:1-禁用;2-启用;',
    memo text NULL COMMENT '备注',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '桌台基础配置';

CREATE TABLE tables_bets (
    id int PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    table_id int NOT NULL COMMENT '桌台表ID',
    bet_area char(8) NOT NULL COMMENT '下注区域(庄,闲等)',
    odds decimal(5, 4) NOT NULL COMMENT '赔率',
    max_bet_u decimal(12, 2) NULL DEFAULT 0.00 COMMENT 'U码最大下注金额',
    max_bet_cash decimal(12, 2) NULL DEFAULT 0.00 COMMENT '现金最大下注金额',
    max_bet_chips decimal(12, 2) NULL DEFAULT 0.00 COMMENT '筹码最大下注金额',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '桌台基础配置';

CREATE TABLE tables_start (
    id bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    table_id bigint NOT NULL COMMENT '桌台ID',
    account_period varchar(32) NOT NULL DEFAULT '' COMMENT '账期',
    game_type tinyint(1) NULL COMMENT '游戏类型:1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;-轮盘;',
    stats tinyint(1) NOT NULL DEFAULT 2 COMMENT '当前状态:1-销售中;2-等待洗牌;3-等待出码;',
    shoe_no int NOT NULL COMMENT '场次编号',
    hand_no int NOT NULL COMMENT '局号编号',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '桌台新开账期表';

CREATE TABLE users (
    id bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    wash_code varchar(32) NOT NULL COMMENT '洗码号，用于识别客户身份',
    account_type tinyint(1) NULL DEFAULT 1 COMMENT '账号类型:1-主号;2-分线;',
    main_code varchar(32) NULL COMMENT '所属主号的洗码号',
    name varchar(64) NULL COMMENT '客户姓名',
    phone varchar(32) NULL COMMENT '联系电话',
    password varchar(128) NULL COMMENT '登录密码（加密存储）',
    user_type tinyint(1) NOT NULL COMMENT '客户类型:1-公司客户;2-返点F;3-返点W;4-占成客户;5-特殊客户;',
    rebate_rate decimal(5, 4) NULL DEFAULT 0.0000 COMMENT '返点比例，如0.018表示1.8%',
    is_dragon_tiger tinyint(1) NULL COMMENT '是否龙虎洗码',
    status tinyint(1) NULL DEFAULT 1 COMMENT '账号状态:1-正常;2-冻结;3-禁用;',
    memo text NULL COMMENT '客户备注信息',
    operator_remark text NULL COMMENT '操作人员备注',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    again_create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '重开户时间',
    update_time datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    CONSTRAINT wash_code UNIQUE wash_code (wash_code)
) COMMENT '客户信息表';

CREATE TABLE users_update_log (
    id bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    wash_code varchar(32) NOT NULL COMMENT '洗码号',
    name varchar(64) NULL COMMENT '客户姓名',
    before_name varchar(64) NULL COMMENT '客户姓名(修改前)',
    before_client_type tinyint(1) NOT NULL COMMENT '客户类型:1-公司客户;2-返点F;3-返点W;4-占成客户;5-特殊客户;',
    after_name varchar(64) NULL COMMENT '客户姓名(修改后)',
    after_client_type tinyint(1) NOT NULL COMMENT '客户类型:1-公司客户;2-返点F;3-返点W;4-占成客户;5-特殊客户;',
    update_user_id int NOT NULL COMMENT '修改人编号',
    update_user_name varchar(32) NULL DEFAULT '' COMMENT '修改人名称',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    CONSTRAINT wash_code UNIQUE wash_code (wash_code)
) COMMENT '客户信息修改记录表';

CREATE TABLE users_use_log (
    id bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    wash_code varchar(32) NOT NULL COMMENT '洗码号，用于识别客户身份',
    name varchar(64) NULL COMMENT '客户姓名',
    use_begin_time datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '使用开始时间',
    use_end_time datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '使用结束时间',
    update_user_id int NOT NULL COMMENT '修改人编号',
    update_user_name varchar(32) NULL DEFAULT '' COMMENT '修改人名称',
    create_time datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    CONSTRAINT wash_code UNIQUE wash_code (wash_code)
) COMMENT '客户使用记录表';