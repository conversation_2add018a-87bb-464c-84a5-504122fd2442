-- 插入收盘展示数据测试数据
-- 用于测试get_settlement_display接口

-- 清理现有测试数据
DELETE FROM settlement_records WHERE table_id = 1 AND account_period = '********';

-- 插入筹码类型的点码记录
INSERT INTO settlement_records (
    table_id, tables_name, shoe_no, account_period, chips_output, chips_add, currency_type,
    chips_20W, chips_10W, chips_5W, chips_1W, chips_5K, chips_1K, chips_500, chips_100, chips_50, chips_10, chips_5,
    total_amount, client_win_loss, amount_tip, amount_bottom, wash_rate, wash_amount, wash_tip,
    compare_result, remark, operator, create_time
) VALUES (
    1, '测试桌台1', 1, '********', 500000.00, 100000.00, 1,
    2, 3, 5, 10, 20, 50, 100, 200, 100, 50, 20,
    600000.00, -50000.00, 5000.00, 1000.00, 0.01, 50000.00, 500.00,
    '点码与注单一致', '筹码测试数据', 'admin', NOW()
);

-- 插入现金类型的点码记录
INSERT INTO settlement_records (
    table_id, tables_name, shoe_no, account_period, chips_output, chips_add, currency_type,
    chips_20W, chips_10W, chips_5W, chips_1W, chips_5K, chips_1K, chips_500, chips_100, chips_50, chips_10, chips_5,
    total_amount, client_win_loss, amount_tip, amount_bottom, wash_rate, wash_amount, wash_tip,
    compare_result, remark, operator, create_time
) VALUES (
    1, '测试桌台1', 1, '********', 300000.00, 50000.00, 2,
    1, 2, 3, 5, 10, 30, 50, 100, 50, 30, 10,
    350000.00, 25000.00, 3000.00, 500.00, 0.01, 0.00, 0.00,
    '点码多于注单 +25000', '现金测试数据', 'admin', NOW()
);

-- 插入U码类型的点码记录
INSERT INTO settlement_records (
    table_id, tables_name, shoe_no, account_period, chips_output, chips_add, currency_type,
    chips_20W, chips_10W, chips_5W, chips_1W, chips_5K, chips_1K, chips_500, chips_100, chips_50, chips_10, chips_5,
    total_amount, client_win_loss, amount_tip, amount_bottom, wash_rate, wash_amount, wash_tip,
    compare_result, remark, operator, create_time
) VALUES (
    1, '测试桌台1', 1, '********', 200000.00, 30000.00, 3,
    0, 1, 2, 4, 8, 20, 30, 80, 40, 20, 5,
    230000.00, -15000.00, 2000.00, 300.00, 0.01, 15000.00, 150.00,
    '点码少于注单 -15000', 'U码测试数据', 'admin', NOW()
);

-- 插入一条更新的筹码记录（测试最新记录逻辑）
INSERT INTO settlement_records (
    table_id, tables_name, shoe_no, account_period, chips_output, chips_add, currency_type,
    chips_20W, chips_10W, chips_5W, chips_1W, chips_5K, chips_1K, chips_500, chips_100, chips_50, chips_10, chips_5,
    total_amount, client_win_loss, amount_tip, amount_bottom, wash_rate, wash_amount, wash_tip,
    compare_result, remark, operator, create_time
) VALUES (
    1, '测试桌台1', 1, '********', 550000.00, 120000.00, 1,
    2, 3, 6, 12, 25, 55, 110, 220, 110, 55, 25,
    670000.00, -60000.00, 6000.00, 1200.00, 0.01, 60000.00, 600.00,
    '点码与注单一致（更新）', '筹码更新测试数据', 'admin', DATE_ADD(NOW(), INTERVAL 1 MINUTE)
);

-- 查询插入的测试数据
SELECT 
    id,
    currency_type,
    CASE currency_type 
        WHEN 1 THEN '筹码'
        WHEN 2 THEN '现金' 
        WHEN 3 THEN 'U码'
        ELSE '未知'
    END as currency_name,
    total_amount,
    compare_result,
    create_time
FROM settlement_records 
WHERE table_id = 1 AND account_period = '********'
ORDER BY currency_type, create_time DESC;

-- 验证查询逻辑（模拟接口查询）
-- 这个查询应该返回每种货币类型的最新记录
SELECT DISTINCT
    currency_type,
    CASE currency_type 
        WHEN 1 THEN '筹码'
        WHEN 2 THEN '现金' 
        WHEN 3 THEN 'U码'
        ELSE '未知'
    END as currency_name,
    total_amount,
    compare_result,
    create_time
FROM settlement_records s1
WHERE table_id = 1 AND account_period = '********'
AND create_time = (
    SELECT MAX(create_time) 
    FROM settlement_records s2 
    WHERE s2.table_id = s1.table_id 
    AND s2.account_period = s1.account_period 
    AND s2.currency_type = s1.currency_type
)
ORDER BY currency_type;
